{"version": 3, "file": "emitDomEvent.js", "sourceRoot": "", "sources": ["../../src/domComponents/emitDomEvent.ts"], "names": [], "mappings": ";;AAkBA,4CAIC;AAED,wCAEC;AAED,sCAEC;AAED,8CAEC;AAED,4CAEC;AAtCD,qCAMkB;AAGlB,SAAS,YAAY,CAAC,IAAY,EAAE,OAAY,EAAE;IAChD,oFAAoF;IACpF,IAAI,OAAO,kBAAkB,KAAK,WAAW,EAAE,CAAC;QAC7C,MAAc,CAAC,kBAAkB,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAC/E,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAgB,gBAAgB,CAC9B,SAA4E,EAAE;IAE9E,OAAO,YAAY,CAAC,+BAAsB,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;AAC1D,CAAC;AAED,SAAgB,cAAc,CAAC,KAAc;IAC3C,OAAO,YAAY,CAAC,4BAAmB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;AACtD,CAAC;AAED,SAAgB,aAAa;IAC3B,OAAO,YAAY,CAAC,yBAAgB,CAAC,CAAC;AACxC,CAAC;AAED,SAAgB,iBAAiB;IAC/B,OAAO,YAAY,CAAC,gCAAuB,CAAC,CAAC;AAC/C,CAAC;AAED,SAAgB,gBAAgB,CAAC,IAAY,EAAE,OAAsB;IACnE,OAAO,YAAY,CAAC,yBAAgB,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;AAC3D,CAAC", "sourcesContent": ["import {\n  ROUTER_SET_PARAMS_TYPE,\n  ROUTER_DISMISS_TYPE,\n  ROUTER_BACK_TYPE,\n  ROUTER_DISMISS_ALL_TYPE,\n  ROUTER_LINK_TYPE,\n} from './events';\nimport { LinkToOptions } from '../global-state/routing';\n\nfunction emitDomEvent(type: string, data: any = {}) {\n  // @ts-expect-error: ReactNativeWebView is a global variable injected by the WebView\n  if (typeof ReactNativeWebView !== 'undefined') {\n    (window as any).ReactNativeWebView.postMessage(JSON.stringify({ type, data }));\n    return true;\n  }\n  return false;\n}\n\nexport function emitDomSetParams(\n  params: Record<string, undefined | string | number | (string | number)[]> = {}\n) {\n  return emitDomEvent(ROUTER_SET_PARAMS_TYPE, { params });\n}\n\nexport function emitDomDismiss(count?: number) {\n  return emitDomEvent(ROUTER_DISMISS_TYPE, { count });\n}\n\nexport function emitDomGoBack() {\n  return emitDomEvent(ROUTER_BACK_TYPE);\n}\n\nexport function emitDomDismissAll() {\n  return emitDomEvent(ROUTER_DISMISS_ALL_TYPE);\n}\n\nexport function emitDomLinkEvent(href: string, options: LinkToOptions) {\n  return emitDomEvent(ROUTER_LINK_TYPE, { href, options });\n}\n"]}