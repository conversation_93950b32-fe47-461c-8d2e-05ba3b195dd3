{"version": 3, "file": "useDomComponentNavigation.js", "sourceRoot": "", "sources": ["../../src/domComponents/useDomComponentNavigation.ts"], "names": [], "mappings": ";;;;;AAYA,8DAyBC;AArCD,4CAA4D;AAC5D,kDAA0B;AAE1B,qCAMkB;AAClB,qDAAyF;AAEzF,SAAgB,yBAAyB;IACvC,eAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;YAClC,OAAO,GAAG,EAAE,GAAE,CAAC,CAAC;QAClB,CAAC;QACD,OAAO,IAAA,kCAAyB,EAAM,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;YACvD,QAAQ,IAAI,EAAE,CAAC;gBACb,KAAK,yBAAgB;oBACnB,IAAA,gBAAM,EAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;oBAChC,MAAM;gBACR,KAAK,gCAAuB;oBAC1B,IAAA,oBAAU,GAAE,CAAC;oBACb,MAAM;gBACR,KAAK,4BAAmB;oBACtB,IAAA,iBAAO,EAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACpB,MAAM;gBACR,KAAK,yBAAgB;oBACnB,IAAA,gBAAM,GAAE,CAAC;oBACT,MAAM;gBACR,KAAK,+BAAsB;oBACzB,IAAA,mBAAS,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBACvB,MAAM;YACV,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC", "sourcesContent": ["import { addGlobalDomEventListener } from 'expo/dom/global';\nimport React from 'react';\n\nimport {\n  ROUTER_LINK_TYPE,\n  ROUTER_DISMISS_ALL_TYPE,\n  ROUTER_DISMISS_TYPE,\n  ROUTER_BACK_TYPE,\n  ROUTER_SET_PARAMS_TYPE,\n} from './events';\nimport { dismiss, dismissAll, goBack, linkTo, setParams } from '../global-state/routing';\n\nexport function useDomComponentNavigation() {\n  React.useEffect(() => {\n    if (process.env.EXPO_OS === 'web') {\n      return () => {};\n    }\n    return addGlobalDomEventListener<any>(({ type, data }) => {\n      switch (type) {\n        case ROUTER_LINK_TYPE:\n          linkTo(data.href, data.options);\n          break;\n        case ROUTER_DISMISS_ALL_TYPE:\n          dismissAll();\n          break;\n        case ROUTER_DISMISS_TYPE:\n          dismiss(data.count);\n          break;\n        case ROUTER_BACK_TYPE:\n          goBack();\n          break;\n        case ROUTER_SET_PARAMS_TYPE:\n          setParams(data.params);\n          break;\n      }\n    });\n  }, []);\n}\n"]}