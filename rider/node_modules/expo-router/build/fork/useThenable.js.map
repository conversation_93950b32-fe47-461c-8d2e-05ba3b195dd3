{"version": 3, "file": "useThenable.js", "sourceRoot": "", "sources": ["../../src/fork/useThenable.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,kCAsCC;AA3CD;;GAEG;AACH,6CAA+B;AAE/B,SAAgB,WAAW,CAAI,MAA4B;IACzD,MAAM,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAEzC,IAAI,YAAY,GAA6B,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IAEhE,uCAAuC;IACvC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;QACtB,YAAY,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IACvD,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;IAEzB,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,IAAI,SAAS,GAAG,KAAK,CAAC;QAEtB,MAAM,OAAO,GAAG,KAAK,IAAI,EAAE;YACzB,IAAI,MAAM,CAAC;YAEX,IAAI,CAAC;gBACH,MAAM,GAAG,MAAM,OAAO,CAAC;YACzB,CAAC;oBAAS,CAAC;gBACT,IAAI,CAAC,SAAS,EAAE,CAAC;oBACf,QAAQ,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;gBAC3B,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,GAAG,EAAE;YACV,SAAS,GAAG,IAAI,CAAC;QACnB,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;IAExB,OAAO,KAAK,CAAC;AACf,CAAC", "sourcesContent": ["/*\n * This file is unchanged, except for moving eslint comments\n */\nimport * as React from 'react';\n\nexport function useThenable<T>(create: () => PromiseLike<T>) {\n  const [promise] = React.useState(create);\n\n  let initialState: [boolean, T | undefined] = [false, undefined];\n\n  // Check if our thenable is synchronous\n  promise.then((result) => {\n    initialState = [true, result];\n  });\n\n  const [state, setState] = React.useState(initialState);\n  const [resolved] = state;\n\n  React.useEffect(() => {\n    let cancelled = false;\n\n    const resolve = async () => {\n      let result;\n\n      try {\n        result = await promise;\n      } finally {\n        if (!cancelled) {\n          setState([true, result]);\n        }\n      }\n    };\n\n    if (!resolved) {\n      resolve();\n    }\n\n    return () => {\n      cancelled = true;\n    };\n  }, [promise, resolved]);\n\n  return state;\n}\n"]}