{"version": 3, "file": "useLinking.native.js", "sourceRoot": "", "sources": ["../../src/fork/useLinking.native.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,gCA8LC;AAED,4DAiBC;AArOD,qDAMkC;AAElC,0DAA4C;AAC5C,6CAA+B;AAC/B,+CAAiD;AAEjD,6DAA8D;AAM9D,MAAM,eAAe,GAAa,EAAE,CAAC;AAErC,SAAgB,UAAU,CACxB,GAA2D,EAC3D,EACE,OAAO,GAAG,IAAI,EACd,QAAQ,EACR,MAAM,EACN,MAAM,EACN,aAAa,GAAG,GAAG,EAAE,CAAC,wBAAwB,EAAE,EAChD,SAAS,GAAG,CAAC,QAAQ,EAAE,EAAE;IACvB,MAAM,QAAQ,GAAG,CAAC,EAAE,GAAG,EAAmB,EAAE,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IAE7D,MAAM,YAAY,GAAG,sBAAO,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,CAEhD,CAAC;IAEd,2FAA2F;IAC3F,4EAA4E;IAC5E,MAAM,mBAAmB,GAAG,sBAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,sBAAO,CAAC,CAAC;IAEvE,OAAO,GAAG,EAAE;QACV,2FAA2F;QAC3F,IAAI,YAAY,EAAE,MAAM,EAAE,CAAC;YACzB,YAAY,CAAC,MAAM,EAAE,CAAC;QACxB,CAAC;aAAM,CAAC;YACN,mBAAmB,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACzC,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,EACD,gBAAgB,GAAG,yBAAuB,EAC1C,kBAAkB,GAAG,2BAAyB,GACtC,EACV,kBAAqE;IAErE,MAAM,WAAW,GAAG,IAAA,qCAA4B,GAAE,CAAC;IAEnD,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC1C,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,WAAW,EAAE,CAAC;YAChB,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,OAAO,KAAK,KAAK,IAAI,eAAe,CAAC,MAAM,EAAE,CAAC;YAChD,OAAO,CAAC,KAAK,CACX;gBACE,6KAA6K;gBAC7K,uFAAuF;gBACvF,4DAA4D;gBAC5D,uBAAQ,CAAC,EAAE,KAAK,SAAS;oBACvB,CAAC,CAAC,sJAAsJ;oBACxJ,CAAC,CAAC,EAAE;aACP;iBACE,IAAI,CAAC,IAAI,CAAC;iBACV,IAAI,EAAE,CACV,CAAC;QACJ,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC;QAEzB,IAAI,OAAO,KAAK,KAAK,EAAE,CAAC;YACtB,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChC,CAAC;QAED,OAAO,GAAG,EAAE;YACV,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAE/C,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;gBACf,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACnC,CAAC;QACH,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;IAE3B,kGAAkG;IAClG,oFAAoF;IACpF,yGAAyG;IACzG,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACzC,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC3C,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACvC,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACvC,MAAM,gBAAgB,GAAG,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IACrD,MAAM,mBAAmB,GAAG,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IAC3D,MAAM,qBAAqB,GAAG,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;IAE/D,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAC7B,WAAW,CAAC,OAAO,GAAG,QAAQ,CAAC;QAC/B,SAAS,CAAC,OAAO,GAAG,MAAM,CAAC;QAC3B,SAAS,CAAC,OAAO,GAAG,MAAM,CAAC;QAC3B,gBAAgB,CAAC,OAAO,GAAG,aAAa,CAAC;QACzC,mBAAmB,CAAC,OAAO,GAAG,gBAAgB,CAAC;QAC/C,qBAAqB,CAAC,OAAO,GAAG,kBAAkB,CAAC;IACrD,CAAC,CAAC,CAAC;IAEH,MAAM,eAAe,GAAG,KAAK,CAAC,WAAW,CACvC,CAAC,GAA8B,EAAE,EAAE;QACjC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YAC3D,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,IAAI,GAAG,IAAA,2CAAsB,EAAC,WAAW,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QAE9D,OAAO,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAC/F,CAAC,EAED,EAAE,CACH,CAAC;IAEF,MAAM,eAAe,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE;QAC7C,IAAI,KAA8B,CAAC;QAEnC,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;YACvB,MAAM,GAAG,GAAG,gBAAgB,CAAC,OAAO,EAAE,CAAC;YAEvC,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;gBAChB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;oBAC5B,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;wBACtB,MAAM,KAAK,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;wBAEnC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;4BAC5B,mEAAmE;4BACnE,kBAAkB,CAAC,IAAA,2CAAsB,EAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;wBAC5D,CAAC;wBAED,OAAO,KAAK,CAAC;oBACf,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,kBAAkB,CAAC,IAAA,2CAAsB,EAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC5D,CAAC;YACH,CAAC;YAED,KAAK,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;QAC/B,CAAC;QAED,MAAM,QAAQ,GAAG;YACf,IAAI,CAAC,WAAsD;gBACzD,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YACnE,CAAC;YACD,KAAK;gBACH,OAAO,QAAQ,CAAC;YAClB,CAAC;SACF,CAAC;QAEF,OAAO,QAAgD,CAAC;IAC1D,CAAC,EAAE,CAAC,eAAe,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC,CAAC;IAEpD,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,MAAM,QAAQ,GAAG,CAAC,GAAW,EAAE,EAAE;YAC/B,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO;YACT,CAAC;YAED,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC;YAC/B,MAAM,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAE5D,IAAI,UAAU,IAAI,KAAK,EAAE,CAAC;gBACxB,mEAAmE;gBACnE,kBAAkB,CAAC,IAAA,2CAAsB,EAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC1D,MAAM,SAAS,GAAG,UAAU,CAAC,YAAY,EAAE,CAAC;gBAC5C,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;oBACtE,OAAO;gBACT,CAAC;gBAED,MAAM,MAAM,GAAG,qBAAqB,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;gBAEvE,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;oBACzB,IAAI,CAAC;wBACH,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;oBAC9B,CAAC;oBAAC,OAAO,CAAC,EAAE,CAAC;wBACX,uCAAuC;wBACvC,6FAA6F;wBAC7F,OAAO,CAAC,IAAI,CACV,qDAAqD,GAAG,MACtD,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,IAAI,IAAI,IAAI,SAAS,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CACrE,EAAE,CACH,CAAC;oBACJ,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBAC9B,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QAEF,OAAO,SAAS,CAAC,QAAQ,CAAC,CAAC;IAC7B,CAAC,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,kBAAkB,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC,CAAC;IAE7E,OAAO;QACL,eAAe;KAChB,CAAC;AACJ,CAAC;AAED,SAAgB,wBAAwB;IACtC,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;QAClC,OAAO,EAAE,CAAC;IACZ,CAAC;SAAM,IAAI,uBAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;QACjC,mFAAmF;QACnF,OAAO,WAAW,CAAC,aAAa,EAAE,CAAC;IACrC,CAAC;IAED,OAAO,OAAO,CAAC,IAAI,CAAC;QAClB,4DAA4D;QAC5D,sBAAO,CAAC,aAAa,EAAE;QACvB,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;QAC5B,wDAAwD;QACxD,uEAAuE;QACvE,UAAU,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CACrC;KACF,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import {\n  getActionFromState as getActionFromStateDefault,\n  getStateFromPath as getStateFromPathDefault,\n  type NavigationContainerRef,\n  type ParamListBase,\n  useNavigationIndependentTree,\n} from '@react-navigation/native';\nimport { LinkingOptions } from '@react-navigation/native';\nimport * as ExpoLinking from 'expo-linking';\nimport * as React from 'react';\nimport { Linking, Platform } from 'react-native';\n\nimport { extractExpoPathFromURL } from './extractPathFromURL';\n\ntype ResultState = ReturnType<typeof getStateFromPathDefault>;\n\ntype Options = LinkingOptions<ParamListBase>;\n\nconst linkingHandlers: symbol[] = [];\n\nexport function useLinking(\n  ref: React.RefObject<NavigationContainerRef<ParamListBase>>,\n  {\n    enabled = true,\n    prefixes,\n    filter,\n    config,\n    getInitialURL = () => getInitialURLWithTimeout(),\n    subscribe = (listener) => {\n      const callback = ({ url }: { url: string }) => listener(url);\n\n      const subscription = Linking.addEventListener('url', callback) as\n        | { remove(): void }\n        | undefined;\n\n      // Storing this in a local variable stops <PERSON><PERSON> from complaining about import after teardown\n      // @ts-expect-error: removeEventListener is not present in newer RN versions\n      const removeEventListener = Linking.removeEventListener?.bind(Linking);\n\n      return () => {\n        // https://github.com/facebook/react-native/commit/6d1aca806cee86ad76de771ed3a1cc62982ebcd7\n        if (subscription?.remove) {\n          subscription.remove();\n        } else {\n          removeEventListener?.('url', callback);\n        }\n      };\n    },\n    getStateFromPath = getStateFromPathDefault,\n    getActionFromState = getActionFromStateDefault,\n  }: Options,\n  onUnhandledLinking: (lastUnhandledLining: string | undefined) => void\n) {\n  const independent = useNavigationIndependentTree();\n\n  React.useEffect(() => {\n    if (process.env.NODE_ENV === 'production') {\n      return undefined;\n    }\n\n    if (independent) {\n      return undefined;\n    }\n\n    if (enabled !== false && linkingHandlers.length) {\n      console.error(\n        [\n          'Looks like you have configured linking in multiple places. This is likely an error since deep links should only be handled in one place to avoid conflicts. Make sure that:',\n          \"- You don't have multiple NavigationContainers in the app each with 'linking' enabled\",\n          '- Only a single instance of the root component is rendered',\n          Platform.OS === 'android'\n            ? \"- You have set 'android:launchMode=singleTask' in the '<activity />' section of the 'AndroidManifest.xml' file to avoid launching multiple instances\"\n            : '',\n        ]\n          .join('\\n')\n          .trim()\n      );\n    }\n\n    const handler = Symbol();\n\n    if (enabled !== false) {\n      linkingHandlers.push(handler);\n    }\n\n    return () => {\n      const index = linkingHandlers.indexOf(handler);\n\n      if (index > -1) {\n        linkingHandlers.splice(index, 1);\n      }\n    };\n  }, [enabled, independent]);\n\n  // We store these options in ref to avoid re-creating getInitialState and re-subscribing listeners\n  // This lets user avoid wrapping the items in `React.useCallback` or `React.useMemo`\n  // Not re-creating `getInitialState` is important coz it makes it easier for the user to use in an effect\n  const enabledRef = React.useRef(enabled);\n  const prefixesRef = React.useRef(prefixes);\n  const filterRef = React.useRef(filter);\n  const configRef = React.useRef(config);\n  const getInitialURLRef = React.useRef(getInitialURL);\n  const getStateFromPathRef = React.useRef(getStateFromPath);\n  const getActionFromStateRef = React.useRef(getActionFromState);\n\n  React.useEffect(() => {\n    enabledRef.current = enabled;\n    prefixesRef.current = prefixes;\n    filterRef.current = filter;\n    configRef.current = config;\n    getInitialURLRef.current = getInitialURL;\n    getStateFromPathRef.current = getStateFromPath;\n    getActionFromStateRef.current = getActionFromState;\n  });\n\n  const getStateFromURL = React.useCallback(\n    (url: string | null | undefined) => {\n      if (!url || (filterRef.current && !filterRef.current(url))) {\n        return undefined;\n      }\n\n      const path = extractExpoPathFromURL(prefixesRef.current, url);\n\n      return path !== undefined ? getStateFromPathRef.current(path, configRef.current) : undefined;\n    },\n\n    []\n  );\n\n  const getInitialState = React.useCallback(() => {\n    let state: ResultState | undefined;\n\n    if (enabledRef.current) {\n      const url = getInitialURLRef.current();\n\n      if (url != null) {\n        if (typeof url !== 'string') {\n          return url.then((url) => {\n            const state = getStateFromURL(url);\n\n            if (typeof url === 'string') {\n              // If the link were handled, it gets cleared in NavigationContainer\n              onUnhandledLinking(extractExpoPathFromURL(prefixes, url));\n            }\n\n            return state;\n          });\n        } else {\n          onUnhandledLinking(extractExpoPathFromURL(prefixes, url));\n        }\n      }\n\n      state = getStateFromURL(url);\n    }\n\n    const thenable = {\n      then(onfulfilled?: (state: ResultState | undefined) => void) {\n        return Promise.resolve(onfulfilled ? onfulfilled(state) : state);\n      },\n      catch() {\n        return thenable;\n      },\n    };\n\n    return thenable as PromiseLike<ResultState | undefined>;\n  }, [getStateFromURL, onUnhandledLinking, prefixes]);\n\n  React.useEffect(() => {\n    const listener = (url: string) => {\n      if (!enabled) {\n        return;\n      }\n\n      const navigation = ref.current;\n      const state = navigation ? getStateFromURL(url) : undefined;\n\n      if (navigation && state) {\n        // If the link were handled, it gets cleared in NavigationContainer\n        onUnhandledLinking(extractExpoPathFromURL(prefixes, url));\n        const rootState = navigation.getRootState();\n        if (state.routes.some((r) => !rootState?.routeNames.includes(r.name))) {\n          return;\n        }\n\n        const action = getActionFromStateRef.current(state, configRef.current);\n\n        if (action !== undefined) {\n          try {\n            navigation.dispatch(action);\n          } catch (e) {\n            // Ignore any errors from deep linking.\n            // This could happen in case of malformed links, navigation object not being initialized etc.\n            console.warn(\n              `An error occurred when trying to handle the link '${url}': ${\n                typeof e === 'object' && e != null && 'message' in e ? e.message : e\n              }`\n            );\n          }\n        } else {\n          navigation.resetRoot(state);\n        }\n      }\n    };\n\n    return subscribe(listener);\n  }, [enabled, getStateFromURL, onUnhandledLinking, prefixes, ref, subscribe]);\n\n  return {\n    getInitialState,\n  };\n}\n\nexport function getInitialURLWithTimeout(): string | null | Promise<string | null> {\n  if (typeof window === 'undefined') {\n    return '';\n  } else if (Platform.OS === 'ios') {\n    // Use the new Expo API for iOS. This has better support for App Clips and handoff.\n    return ExpoLinking.getLinkingURL();\n  }\n\n  return Promise.race([\n    // TODO: Phase this out in favor of expo-linking on Android.\n    Linking.getInitialURL(),\n    new Promise<null>((resolve) =>\n      // Timeout in 150ms if `getInitialState` doesn't resolve\n      // Workaround for https://github.com/facebook/react-native/issues/25675\n      setTimeout(() => resolve(null), 150)\n    ),\n  ]);\n}\n"]}