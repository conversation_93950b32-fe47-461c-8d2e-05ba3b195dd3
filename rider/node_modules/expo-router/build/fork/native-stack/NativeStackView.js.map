{"version": 3, "file": "NativeStackView.js", "sourceRoot": "", "sources": ["../../../src/fork/native-stack/NativeStackView.tsx"], "names": [], "mappings": ";;AAKA,0CAMC;AAXD,iEAAsF;AACtF,iCAAqD;AAErD,uDAA8E;AAE9E,SAAgB,eAAe,CAAC,KAA+C;IAC7E,OAAO,CACL,CAAC,6BAAiB,CAChB;MAAA,CAAC,oBAAoB,CAAC,IAAI,KAAK,CAAC,EAClC;IAAA,EAAE,6BAAiB,CAAC,CACrB,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAAC,KAA+C;IAC3E,MAAM,UAAU,GAAG,IAAA,WAAG,EAAC,4BAAgB,CAAC,CAAC;IAEzC,sCAAsC;IACtC,MAAM,KAAK,GAAG,IAAA,eAAO,EAAC,GAAG,EAAE;QACzB,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,OAAO,KAAK,CAAC,KAAK,CAAC;QACrB,CAAC;QAED,OAAO;YACL,GAAG,KAAK,CAAC,KAAK;YACd,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;SACrD,CAAC;IACJ,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;IAErC,OAAO,CAAC,8BAAiB,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,EAAG,CAAC;AACxD,CAAC", "sourcesContent": ["import { NativeStackView as RNNativeStackView } from '@react-navigation/native-stack';\nimport { ComponentProps, use, useMemo } from 'react';\n\nimport { RootModalContext, RootModalProvider } from '../../layouts/RootModal';\n\nexport function NativeStackView(props: ComponentProps<typeof RNNativeStackView>) {\n  return (\n    <RootModalProvider>\n      <NativeStackViewInner {...props} />\n    </RootModalProvider>\n  );\n}\n\nfunction NativeStackViewInner(props: ComponentProps<typeof RNNativeStackView>) {\n  const rootModals = use(RootModalContext);\n\n  // Append the root modals to the state\n  const state = useMemo(() => {\n    if (rootModals.routes.length === 0) {\n      return props.state;\n    }\n\n    return {\n      ...props.state,\n      routes: props.state.routes.concat(rootModals.routes),\n    };\n  }, [props.state, rootModals.routes]);\n\n  return <RNNativeStackView {...props} state={state} />;\n}\n"]}