{"name": "expo-web-browser", "version": "14.2.0", "description": "Provides access to the system's web browser and supports handling redirects. On iOS, it uses SFSafariViewController or ASWebAuthenticationSession, depending on the method you call, and on Android it uses ChromeCustomTabs. As of iOS 11, SFSafariViewController no longer shares cookies with Safari, so if you are using WebBrowser for authentication you will want to use WebBrowser.openAuthSessionAsync, and if you just want to open a webpage (such as your app privacy policy), then use WebBrowser.openBrowserAsync.", "main": "build/WebBrowser.js", "types": "build/WebBrowser.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-web-browser", "web", "browser"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-web-browser"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/webbrowser/", "jest": {"preset": "expo-module-scripts"}, "devDependencies": {"expo-module-scripts": "^4.1.7"}, "peerDependencies": {"expo": "*", "react-native": "*"}, "gitHead": "97b7218bcff9f9b038cec94dc2817cd8908d9fa1"}