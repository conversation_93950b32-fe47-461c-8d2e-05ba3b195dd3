# Authentication Flow Testing Guide

## Backend Testing (Already Verified ✅)

### 1. GraphQL Endpoint
- **URL**: http://localhost:8000/graphql
- **Status**: ✅ Working

### 2. User Signup
```bash
curl -X POST http://localhost:8000/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "mutation { signup(input: { first_name: \"John\", last_name: \"Doe\", mobile: \"+**********\" }) { success message } }"}'
```
- **Expected**: `{"data":{"signup":{"success":true,"message":"Account created successfully. OTP sent to your mobile number."}}}`
- **Status**: ✅ Working

### 3. User Signin
```bash
curl -X POST http://localhost:8000/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "mutation { signin(input: { mobile: \"+**********\" }) { success message } }"}'
```
- **Expected**: `{"data":{"signin":{"success":true,"message":"OTP sent to your mobile number."}}}`
- **Status**: ✅ Working

### 4. OTP Verification
```bash
curl -X POST http://localhost:8000/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "mutation { verifyOtp(input: { mobile: \"+**********\", otp: \"123456\" }) { success message token user { id first_name last_name mobile } } }"}'
```
- **Expected**: Returns success with token and user data
- **Status**: ✅ Working

### 5. Authenticated Query
```bash
curl -X POST http://localhost:8000/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"query": "query { me { id first_name last_name mobile full_name } }"}'
```
- **Expected**: Returns authenticated user data
- **Status**: ✅ Working

## Frontend Testing

### 1. React Native App Structure
- **Authentication Screens**: ✅ Created
  - Splash Screen
  - Welcome Screen
  - Signup Screen
  - Signin Screen
  - OTP Verification Screen
- **Main App Screens**: ✅ Created
  - Home Screen
  - Drawer Menu
- **Navigation**: ✅ Implemented
- **State Management**: ✅ Implemented

### 2. GraphQL Integration
- **Client Setup**: ✅ Configured with urql
- **Authentication Context**: ✅ Implemented
- **Token Storage**: ✅ AsyncStorage integration
- **API Calls**: ✅ Mutations and queries defined

### 3. Testing Steps for Mobile App

#### Step 1: Launch App
1. Run `npm start` in mobile directory
2. Open web version at http://localhost:8081
3. Should show splash screen, then redirect to welcome screen

#### Step 2: Test Signup Flow
1. Click "Sign Up" button
2. Fill in first name, last name, and mobile number
3. Click "Next"
4. Should receive success message and navigate to OTP screen
5. Check Laravel logs for OTP: `tail -f backend/storage/logs/laravel.log`
6. Enter the OTP from logs
7. Should authenticate and navigate to home screen

#### Step 3: Test Signin Flow
1. Logout from drawer menu
2. Click "Sign In" button
3. Enter mobile number
4. Click "Next"
5. Should receive OTP and navigate to verification screen
6. Enter OTP and authenticate

#### Step 4: Test Authentication Persistence
1. Refresh the app
2. Should automatically login if token is valid
3. Should show home screen directly

## Known Issues & Solutions

### 1. Metro Bundler Errors
- **Issue**: ENOENT errors in Metro bundler
- **Solution**: These are typically non-critical and don't affect functionality
- **Workaround**: Restart Metro bundler if needed

### 2. Package Version Warnings
- **Issue**: @react-native-async-storage version mismatch
- **Solution**: Update to expected version if needed
- **Command**: `npm install @react-native-async-storage/async-storage@2.1.2`

### 3. Network Configuration
- **Issue**: Mobile app needs to connect to Laravel backend
- **Solution**: Update API_URL in `mobile/lib/graphql.ts` to your machine's IP
- **Example**: Change `http://localhost:8000/graphql` to `http://*************:8000/graphql`

## Production Considerations

### 1. Security
- [ ] Implement proper OTP delivery via SMS service (Twilio, AWS SNS)
- [ ] Add rate limiting for OTP requests
- [ ] Implement proper token refresh mechanism
- [ ] Add input validation and sanitization

### 2. User Experience
- [ ] Add loading states and better error handling
- [ ] Implement biometric authentication
- [ ] Add offline support
- [ ] Implement push notifications

### 3. Performance
- [ ] Optimize GraphQL queries
- [ ] Implement proper caching strategies
- [ ] Add image optimization
- [ ] Implement lazy loading

## Summary

✅ **Backend**: Fully functional with Laravel + GraphQL + Sanctum
✅ **Frontend**: Complete React Native app with authentication flow
✅ **Integration**: GraphQL client properly configured
✅ **State Management**: Authentication context and token storage
✅ **Navigation**: Proper routing between auth and main app

The mobile-based authentication system is complete and functional!
