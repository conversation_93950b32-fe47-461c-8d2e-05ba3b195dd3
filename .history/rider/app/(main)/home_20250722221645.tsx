import React, { useState, useEffect } from 'react';
import RiderMap from '../../components/RiderMap';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, Modal, Alert, ActivityIndicator, TextInput } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import DrawerContent from '../../components/DrawerContent';
import { REQUEST_TRIP_MUTATION, GET_CURRENT_TRIP_QUERY, CANCEL_TRIP_MUTATION, UPDATE_USER_LOCATION_MUTATION, UPDATE_USER_FCM_TOKEN_MUTATION } from '../../lib/graphql';
import * as Location from 'expo-location';
// import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';

interface Trip {
  id: string;
  status: string;
  pickup_address: string;
  destination_address: string;
  estimated_fare: number;
  estimated_distance: number;
  estimated_duration: number;
  driver?: {
    id: string;
    first_name: string;
    last_name: string;
  };
}

export default function HomeScreen() {
  const { user, token } = useAuth();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [currentTrip, setCurrentTrip] = useState<Trip | null>(null);
  const [destination, setDestination] = useState('');
  const [currentLocation, setCurrentLocation] = useState<Location.LocationObject | null>(null);
  const [loading, setLoading] = useState(false);
  const [showTripRequest, setShowTripRequest] = useState(false);

  useEffect(() => {
    getCurrentLocation();
    checkCurrentTrip();
    updateFCMToken();

    // Poll for trip updates every 5 seconds
    const interval = setInterval(checkCurrentTrip, 5000);
    return () => clearInterval(interval);
  }, []);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission denied', 'Location permission is required to use this app.');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      setCurrentLocation(location);

      // Update location in backend
      if (token) {
        try {
          const response = await fetch('http://********:8001/graphql', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`,
            },
            body: JSON.stringify({
              query: UPDATE_USER_LOCATION_MUTATION,
              variables: {
                latitude: location.coords.latitude,
                longitude: location.coords.longitude,
              },
            }),
          });

          const data = await response.json();
          if (data.errors) {
            console.error('Location update errors:', data.errors);
          } else if (data.data?.updateUserLocation?.success) {
            console.log('Location updated successfully');
          }
        } catch (error) {
          console.error('Error updating location:', error);
        }
      }
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  const updateFCMToken = async () => {
    if (!token) return;

    try {
      // For demo purposes, generate a mock FCM token
      const mockFCMToken = `fcm_token_${Date.now()}`;

      const response = await fetch('http://********:8001/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          query: UPDATE_USER_FCM_TOKEN_MUTATION,
          variables: {
            token: mockFCMToken,
          },
        }),
      });

      const data = await response.json();
      if (data.errors) {
        console.error('FCM token update errors:', data.errors);
      } else if (data.data?.updateUserFCMToken?.success) {
        console.log('FCM token updated successfully');
      }
    } catch (error) {
      console.error('Error updating FCM token:', error);
    }
  };

  const checkCurrentTrip = async () => {
    if (!token) return;

    try {
      const response = await fetch('http://********:8001/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          query: GET_CURRENT_TRIP_QUERY,
        }),
      });

      if (!response.ok) {
        console.error('HTTP error:', response.status, response.statusText);
        return;
      }

      const data = await response.json();

      if (data.errors) {
        console.error('GraphQL errors:', data.errors);
        return;
      }

      if (data.data?.getCurrentTrip) {
        setCurrentTrip(data.data.getCurrentTrip);
      } else {
        setCurrentTrip(null);
      }
    } catch (error) {
      console.error('Error checking current trip:', error);
      // Don't show error to user if there's no current trip - this is expected
    }
  };

  const requestTrip = async () => {
    if (!destination.trim()) {
      Alert.alert('Error', 'Please enter a destination');
      return;
    }

    if (!currentLocation) {
      Alert.alert('Error', 'Current location not available');
      return;
    }

    setLoading(true);

    try {
      // For demo purposes, use a fixed destination coordinate
      const destinationLat = currentLocation.coords.latitude + 0.01;
      const destinationLng = currentLocation.coords.longitude + 0.01;

      const response = await fetch('http://********:8001/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          query: REQUEST_TRIP_MUTATION,
          variables: {
            input: {
              pickupAddress: 'Current Location',
              pickupLat: currentLocation.coords.latitude,
              pickupLng: currentLocation.coords.longitude,
              destinationAddress: destination,
              destinationLat: destinationLat,
              destinationLng: destinationLng,
            },
          },
        }),
      });

      const data = await response.json();
      if (data.data?.requestTrip?.success) {
        Alert.alert('Success', data.data.requestTrip.message);
        setDestination('');
        setShowTripRequest(false);
        checkCurrentTrip();
      } else {
        Alert.alert('Error', data.data?.requestTrip?.message || 'Failed to request trip');
      }
    } catch (error) {
      console.error('Error requesting trip:', error);
      Alert.alert('Error', 'Failed to request trip');
    } finally {
      setLoading(false);
    }
  };

  const cancelTrip = async () => {
    if (!currentTrip) return;

    Alert.alert(
      'Cancel Trip',
      'Are you sure you want to cancel this trip?',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes',
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await fetch('http://********:8001/graphql', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${token}`,
                },
                body: JSON.stringify({
                  query: CANCEL_TRIP_MUTATION,
                  variables: {
                    tripId: currentTrip.id,
                    reason: 'Cancelled by user',
                  },
                }),
              });

              const data = await response.json();
              if (data.data?.cancelTrip?.success) {
                Alert.alert('Success', 'Trip cancelled successfully');
                setCurrentTrip(null);
              } else {
                Alert.alert('Error', data.data?.cancelTrip?.message || 'Failed to cancel trip');
              }
            } catch (error) {
              console.error('Error cancelling trip:', error);
              Alert.alert('Error', 'Failed to cancel trip');
            }
          },
        },
      ]
    );
  };

  const openDrawer = () => {
    setIsDrawerOpen(true);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={openDrawer} style={styles.menuButton}>
          <Text style={styles.menuIcon}>☰</Text>
        </TouchableOpacity>
        <View style={styles.userInfo}>
          <Text style={styles.greeting}>Good night, {user?.first_name}</Text>
        </View>
      </View>

      <View style={{ flex: 1, margin: 20, borderRadius: 12, overflow: 'hidden' }}>
        <RiderMap />
      </View>

      {/* Trip Status or Request Button */}
      {currentTrip ? (
        <View style={styles.tripStatusContainer}>
          <Text style={styles.tripStatusTitle}>Current Trip</Text>
          <Text style={styles.tripStatusText}>Status: {currentTrip.status.replace('_', ' ').toUpperCase()}</Text>
          <Text style={styles.tripStatusText}>From: {currentTrip.pickup_address}</Text>
          <Text style={styles.tripStatusText}>To: {currentTrip.destination_address}</Text>
          <Text style={styles.tripStatusText}>Fare: ${currentTrip.estimated_fare}</Text>

          {currentTrip.driver && (
            <View style={styles.driverInfo}>
              <Text style={styles.driverTitle}>Driver</Text>
              <Text style={styles.driverText}>{currentTrip.driver.first_name} {currentTrip.driver.last_name}</Text>
            </View>
          )}

          {currentTrip.status === 'requested' && (
            <TouchableOpacity style={styles.cancelButton} onPress={cancelTrip}>
              <Text style={styles.cancelButtonText}>Cancel Trip</Text>
            </TouchableOpacity>
          )}
        </View>
      ) : (
        <View style={styles.requestContainer}>
          <TouchableOpacity
            style={styles.requestButton}
            onPress={() => setShowTripRequest(true)}
          >
            <Text style={styles.requestButtonText}>Where to?</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Trip Request Modal */}
      <Modal
        visible={showTripRequest}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowTripRequest(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.tripRequestModal}>
            <Text style={styles.modalTitle}>Request a Trip</Text>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Destination</Text>
              <TextInput
                style={styles.input}
                value={destination}
                onChangeText={setDestination}
                placeholder="Enter destination address"
                placeholderTextColor="#9CA3AF"
              />
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.modalCancelButton}
                onPress={() => setShowTripRequest(false)}
              >
                <Text style={styles.modalCancelText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.modalRequestButton}
                onPress={requestTrip}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator color="white" />
                ) : (
                  <Text style={styles.modalRequestText}>Request Trip</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

     
      {/* Drawer Modal */}
      <Modal
        visible={isDrawerOpen}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setIsDrawerOpen(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          onPress={() => setIsDrawerOpen(false)}
        >
          <View style={styles.drawerContainer}>
            <DrawerContent />
          </View>
        </TouchableOpacity>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  menuButton: {
    padding: 8,
  },
  menuIcon: {
    fontSize: 24,
    color: '#6B46C1',
  },
  userInfo: {
    flex: 1,
    marginLeft: 16,
  },
  greeting: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  content: {
    padding: 20,
    backgroundColor: 'white',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#6B46C1',
    marginBottom: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  searchIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  searchPlaceholder: {
    fontSize: 16,
    color: '#9CA3AF',
  },
  quickActions: {
    flexDirection: 'row',
    gap: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    gap: 8,
  },
  actionIcon: {
    fontSize: 20,
  },
  actionText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6B46C1',
  },
  mapContainer: {
    flex: 1,
    backgroundColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 20,
    borderRadius: 12,
  },
  mapPlaceholder: {
    fontSize: 18,
    color: '#9CA3AF',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  drawerContainer: {
    width: 280,
    backgroundColor: 'white',
  },
  tripStatusContainer: {
    backgroundColor: 'white',
    margin: 20,
    padding: 20,
    borderRadius: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tripStatusTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#6B46C1',
    marginBottom: 10,
  },
  tripStatusText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 5,
  },
  driverInfo: {
    marginTop: 10,
    padding: 10,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
  },
  driverTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B46C1',
    marginBottom: 5,
  },
  driverText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 2,
  },
  cancelButton: {
    backgroundColor: '#EF4444',
    padding: 12,
    borderRadius: 8,
    marginTop: 15,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  requestContainer: {
    margin: 20,
  },
  requestButton: {
    backgroundColor: '#6B46C1',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  requestButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  tripRequestModal: {
    backgroundColor: 'white',
    width: '90%',
    maxWidth: 400,
    borderRadius: 12,
    padding: 20,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#6B46C1',
    marginBottom: 20,
    textAlign: 'center',
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  modalCancelButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#6B46C1',
    alignItems: 'center',
  },
  modalCancelText: {
    color: '#6B46C1',
    fontWeight: 'bold',
    fontSize: 16,
  },
  modalRequestButton: {
    flex: 1,
    backgroundColor: '#6B46C1',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  modalRequestText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  googlePlacesContainer: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    backgroundColor: 'white',
  },
  googlePlacesInput: {
    fontSize: 16,
    color: '#333',
    paddingHorizontal: 12,
    paddingVertical: 12,
  },
  googlePlacesList: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderTopWidth: 0,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    backgroundColor: 'white',
  },
});
