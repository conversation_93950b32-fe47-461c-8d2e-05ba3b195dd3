import { createClient, cacheExchange, fetchExchange } from 'urql';
import AsyncStorage from '@react-native-async-storage/async-storage';

const API_URL = 'http://localhost:8001/graphql';

// Create the GraphQL client
export const graphqlClient = createClient({
  url: API_URL,
  exchanges: [cacheExchange, fetchExchange],
  fetchOptions: async () => {
    const token = await AsyncStorage.getItem('auth_token');
    return {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    };
  },
});

// GraphQL mutations and queries
export const SIGNUP_MUTATION = `
  mutation UserSignup($input: SignupInput!) {
    userSignup(input: $input) {
      success
      message
    }
  }
`;

export const SIGNIN_MUTATION = `
  mutation UserSignin($input: SigninInput!) {
    userSignin(input: $input) {
      success
      message
    }
  }
`;

export const VERIFY_OTP_MUTATION = `
  mutation UserVerifyOtp($input: VerifyOtpInput!) {
    userVerifyOtp(input: $input) {
      success
      message
      token
      user {
        id
        first_name
        last_name
        mobile
        full_name
      }
    }
  }
`;

export const UPDATE_USER_LOCATION_MUTATION = `
  mutation UpdateUserLocation($latitude: Float!, $longitude: Float!) {
    updateUserLocation(latitude: $latitude, longitude: $longitude) {
      success
      message
    }
  }
`;

export const UPDATE_USER_FCM_TOKEN_MUTATION = `
  mutation UpdateUserFCMToken($token: String!) {
    updateUserFCMToken(token: $token) {
      success
      message
    }
  }
`;

export const REQUEST_TRIP_MUTATION = `
  mutation RequestTrip($input: TripRequestInput!) {
    requestTrip(input: $input) {
      success
      message
      trip {
        id
        status
        pickup_address
        destination_address
        estimated_fare
        estimated_distance
        estimated_duration
      }
    }
  }
`;

export const CANCEL_TRIP_MUTATION = `
  mutation CancelTrip($tripId: ID!, $reason: String) {
    cancelTrip(tripId: $tripId, reason: $reason) {
      success
      message
      reason
    }
  }
`;

export const GET_CURRENT_TRIP_QUERY = `
  query GetCurrentTrip {
    getCurrentTrip {
      id
      status
      pickup_address
      destination_address
      estimated_fare
      estimated_distance
      estimated_duration
      driver {
        id
        first_name
        last_name
        vehicle_type
        license_plate
      }
    }
  }
`;

export const ME_QUERY = `
  query UserMe {
    userMe {
      id
      first_name
      last_name
      mobile
      full_name
      mobile_verified_at
      created_at
    }
  }
`;

// Types
export interface User {
  id: string;
  first_name: string;
  last_name: string;
  mobile: string;
  full_name: string;
  mobile_verified_at?: string;
  created_at: string;
}

export interface SignupInput {
  first_name: string;
  last_name: string;
  mobile: string;
}

export interface SigninInput {
  mobile: string;
}

export interface VerifyOtpInput {
  mobile: string;
  otp: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
}

export interface AuthTokenResponse extends AuthResponse {
  token?: string;
  user?: User;
}
