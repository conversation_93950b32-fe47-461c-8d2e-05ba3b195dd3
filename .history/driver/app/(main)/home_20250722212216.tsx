import React, { useState, useEffect } from 'react';
import DriverMap from '../../components/DriverMap';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, Modal, Alert, ActivityIndicator, Switch } from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import DrawerContent from '../../components/DrawerContent';
import {
  SET_DRIVER_AVAILABILITY_MUTATION,
  UPDATE_DRIVER_LOCATION_MUTATION,
  ACCEPT_TRIP_MUTATION,
  REJECT_TRIP_MUTATION,
  UPDATE_TRIP_STATUS_MUTATION,
  GET_DRIVER_TRIPS_QUERY
} from '../../lib/graphql';
import * as Location from 'expo-location';

interface Trip {
  id: string;
  status: string;
  pickup_address: string;
  destination_address: string;
  estimated_fare: number;
  estimated_distance: number;
  estimated_duration: number;
  user: {
    id: string;
    first_name: string;
    last_name: string;
    mobile: string;
  };
}

export default function HomeScreen() {
  const { driver, token } = useAuth();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isAvailable, setIsAvailable] = useState(false);
  const [currentTrip, setCurrentTrip] = useState<Trip | null>(null);
  const [currentLocation, setCurrentLocation] = useState<Location.LocationObject | null>(null);
  const [loading, setLoading] = useState(false);
  const [tripRequestModal, setTripRequestModal] = useState<Trip | null>(null);

  useEffect(() => {
    getCurrentLocation();

    // Update location every 30 seconds when available
    const locationInterval = setInterval(() => {
      if (isAvailable) {
        getCurrentLocation();
      }
    }, 30000);

    return () => clearInterval(locationInterval);
  }, [isAvailable]);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission denied', 'Location permission is required to use this app.');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      setCurrentLocation(location);

      // Update location in backend
      if (token) {
        await fetch('http://localhost:8000/graphql', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({
            query: UPDATE_DRIVER_LOCATION_MUTATION,
            variables: {
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
            },
          }),
        });
      }
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  const toggleAvailability = async () => {
    setLoading(true);
    const newAvailability = !isAvailable;

    try {
      const response = await fetch('http://localhost:8000/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          query: SET_DRIVER_AVAILABILITY_MUTATION,
          variables: {
            isAvailable: newAvailability,
          },
        }),
      });

      const data = await response.json();
      if (data.data?.setDriverAvailability?.success) {
        setIsAvailable(newAvailability);
        if (newAvailability) {
          getCurrentLocation(); // Update location when going online
        }
      } else {
        Alert.alert('Error', data.data?.setDriverAvailability?.message || 'Failed to update availability');
      }
    } catch (error) {
      console.error('Error updating availability:', error);
      Alert.alert('Error', 'Failed to update availability');
    } finally {
      setLoading(false);
    }
  };

  const acceptTrip = async (tripId: string) => {
    setLoading(true);

    try {
      const response = await fetch('http://localhost:8000/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          query: ACCEPT_TRIP_MUTATION,
          variables: { tripId },
        }),
      });

      const data = await response.json();
      if (data.data?.acceptTrip?.success) {
        setCurrentTrip(data.data.acceptTrip.trip);
        setTripRequestModal(null);
        setIsAvailable(false); // Driver becomes unavailable
        Alert.alert('Success', 'Trip accepted successfully!');
      } else {
        Alert.alert('Error', data.data?.acceptTrip?.message || 'Failed to accept trip');
      }
    } catch (error) {
      console.error('Error accepting trip:', error);
      Alert.alert('Error', 'Failed to accept trip');
    } finally {
      setLoading(false);
    }
  };

  const rejectTrip = async (tripId: string) => {
    try {
      const response = await fetch('http://localhost:8000/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          query: REJECT_TRIP_MUTATION,
          variables: { tripId },
        }),
      });

      const data = await response.json();
      if (data.data?.rejectTrip?.success) {
        setTripRequestModal(null);
      } else {
        Alert.alert('Error', data.data?.rejectTrip?.message || 'Failed to reject trip');
      }
    } catch (error) {
      console.error('Error rejecting trip:', error);
      Alert.alert('Error', 'Failed to reject trip');
    }
  };

  const updateTripStatus = async (status: string) => {
    if (!currentTrip) return;

    setLoading(true);

    try {
      const response = await fetch('http://localhost:8000/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          query: UPDATE_TRIP_STATUS_MUTATION,
          variables: {
            tripId: currentTrip.id,
            status: status
          },
        }),
      });

      const data = await response.json();
      if (data.data?.updateTripStatus?.success) {
        setCurrentTrip(data.data.updateTripStatus.trip);

        if (status === 'trip_completed') {
          setCurrentTrip(null);
          setIsAvailable(true); // Driver becomes available again
          Alert.alert('Success', 'Trip completed successfully!');
        }
      } else {
        Alert.alert('Error', data.data?.updateTripStatus?.message || 'Failed to update trip status');
      }
    } catch (error) {
      console.error('Error updating trip status:', error);
      Alert.alert('Error', 'Failed to update trip status');
    } finally {
      setLoading(false);
    }
  };

  // Simulate receiving trip request (in real app, this would come from FCM)
  const simulateTripRequest = () => {
    const mockTrip: Trip = {
      id: '1',
      status: 'requested',
      pickup_address: '123 Main St, San Francisco',
      destination_address: '456 Market St, San Francisco',
      estimated_fare: 25.50,
      estimated_distance: 2.5,
      estimated_duration: 15,
      user: {
        id: '1',
        first_name: 'John',
        last_name: 'Doe',
        mobile: '+1234567890',
      },
    };
    setTripRequestModal(mockTrip);
  };

  const openDrawer = () => {
    setIsDrawerOpen(true);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={openDrawer} style={styles.menuButton}>
          <Text style={styles.menuIcon}>☰</Text>
        </TouchableOpacity>
        <View style={styles.driverInfo}>
          <Text style={styles.greeting}>Good evening, {driver?.first_name}</Text>
          <Text style={styles.status}>{isAvailable ? "You're online" : "You're offline"}</Text>
        </View>

        <View style={styles.availabilityContainer}>
          <Switch
            value={isAvailable}
            onValueChange={toggleAvailability}
            trackColor={{ false: '#D1D5DB', true: '#059669' }}
            thumbColor={isAvailable ? '#ffffff' : '#f4f3f4'}
            disabled={loading}
          />
        </View>
      </View>

      <View style={{ flex: 1, margin: 20, borderRadius: 12, overflow: 'hidden' }}>
        <DriverMap />
      </View>

      {/* Current Trip Status */}
      {currentTrip && (
        <View style={styles.tripContainer}>
          <Text style={styles.tripTitle}>Current Trip</Text>
          <Text style={styles.tripText}>Status: {currentTrip.status.replace('_', ' ').toUpperCase()}</Text>
          <Text style={styles.tripText}>Passenger: {currentTrip.user.first_name} {currentTrip.user.last_name}</Text>
          <Text style={styles.tripText}>From: {currentTrip.pickup_address}</Text>
          <Text style={styles.tripText}>To: {currentTrip.destination_address}</Text>
          <Text style={styles.tripText}>Fare: ${currentTrip.estimated_fare}</Text>

          <View style={styles.tripActions}>
            {currentTrip.status === 'driver_assigned' && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => updateTripStatus('driver_arriving')}
                disabled={loading}
              >
                <Text style={styles.actionButtonText}>I'm Arriving</Text>
              </TouchableOpacity>
            )}

            {currentTrip.status === 'driver_arriving' && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => updateTripStatus('driver_arrived')}
                disabled={loading}
              >
                <Text style={styles.actionButtonText}>I've Arrived</Text>
              </TouchableOpacity>
            )}

            {currentTrip.status === 'driver_arrived' && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => updateTripStatus('trip_started')}
                disabled={loading}
              >
                <Text style={styles.actionButtonText}>Start Trip</Text>
              </TouchableOpacity>
            )}

            {currentTrip.status === 'trip_started' && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => updateTripStatus('trip_completed')}
                disabled={loading}
              >
                <Text style={styles.actionButtonText}>Complete Trip</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      )}

      {/* Demo Button to Simulate Trip Request */}
      {isAvailable && !currentTrip && (
        <View style={styles.demoContainer}>
          <TouchableOpacity
            style={styles.demoButton}
            onPress={simulateTripRequest}
          >
            <Text style={styles.demoButtonText}>🚗 Simulate Trip Request</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Trip Request Modal */}
      <Modal
        visible={!!tripRequestModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setTripRequestModal(null)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.tripRequestModal}>
            <Text style={styles.modalTitle}>New Trip Request!</Text>

            {tripRequestModal && (
              <>
                <View style={styles.tripDetails}>
                  <Text style={styles.detailText}>Passenger: {tripRequestModal.user.first_name} {tripRequestModal.user.last_name}</Text>
                  <Text style={styles.detailText}>From: {tripRequestModal.pickup_address}</Text>
                  <Text style={styles.detailText}>To: {tripRequestModal.destination_address}</Text>
                  <Text style={styles.detailText}>Distance: {tripRequestModal.estimated_distance} km</Text>
                  <Text style={styles.detailText}>Estimated Fare: ${tripRequestModal.estimated_fare}</Text>
                  <Text style={styles.detailText}>Duration: ~{tripRequestModal.estimated_duration} mins</Text>
                </View>

                <View style={styles.modalButtons}>
                  <TouchableOpacity
                    style={styles.rejectButton}
                    onPress={() => rejectTrip(tripRequestModal.id)}
                  >
                    <Text style={styles.rejectButtonText}>Reject</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.acceptButton}
                    onPress={() => acceptTrip(tripRequestModal.id)}
                    disabled={loading}
                  >
                    {loading ? (
                      <ActivityIndicator color="white" />
                    ) : (
                      <Text style={styles.acceptButtonText}>Accept</Text>
                    )}
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>

      {/* Drawer Modal */}
      <Modal
        visible={isDrawerOpen}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setIsDrawerOpen(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay}
          onPress={() => setIsDrawerOpen(false)}
        >
          <View style={styles.drawerContainer}>
            <DrawerContent />
          </View>
        </TouchableOpacity>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  menuButton: {
    padding: 8,
  },
  menuIcon: {
    fontSize: 24,
    color: '#059669',
  },
  driverInfo: {
    flex: 1,
    marginLeft: 16,
  },
  greeting: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  status: {
    fontSize: 14,
    color: '#10B981',
    fontWeight: '500',
  },
  content: {
    padding: 20,
    backgroundColor: 'white',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#059669',
    marginBottom: 20,
  },
  statusContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  statusButton: {
    backgroundColor: '#10B981',
    paddingHorizontal: 40,
    paddingVertical: 16,
    borderRadius: 25,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  statusIcon: {
    fontSize: 24,
  },
  statusText: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  quickActions: {
    flexDirection: 'row',
    gap: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    gap: 8,
  },
  actionIcon: {
    fontSize: 20,
  },
  actionText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#059669',
  },
  mapContainer: {
    flex: 1,
    backgroundColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 20,
    borderRadius: 12,
  },
  mapPlaceholder: {
    fontSize: 18,
    color: '#9CA3AF',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    flexDirection: 'row',
  },
  drawerContainer: {
    width: 280,
    backgroundColor: 'white',
  },
});
