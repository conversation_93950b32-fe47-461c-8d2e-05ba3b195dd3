import { createClient, cacheExchange, fetchExchange } from 'urql';
import AsyncStorage from '@react-native-async-storage/async-storage';

const API_URL = 'http://10.0.2.2:8000/graphql';

// Create the GraphQL client
export const graphqlClient = createClient({
  url: API_URL,
  exchanges: [cacheExchange, fetchExchange],
  fetchOptions: async () => {
    const token = await AsyncStorage.getItem('driver_auth_token');
    return {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    };
  },
});

// GraphQL mutations and queries
export const SIGNUP_MUTATION = `
  mutation DriverSignup($input: DriverSignupInput!) {
    driverSignup(input: $input) {
      success
      message
    }
  }
`;

export const SIGNIN_MUTATION = `
  mutation DriverSignin($input: DriverSigninInput!) {
    driverSignin(input: $input) {
      success
      message
    }
  }
`;

export const VERIFY_OTP_MUTATION = `
  mutation DriverVerifyOtp($input: VerifyOtpInput!) {
    driverVerifyOtp(input: $input) {
      success
      message
      token
      user {
        id
        first_name
        last_name
        mobile
        full_name
      }
    }
  }
`;

export const UPDATE_DRIVER_LOCATION_MUTATION = `
  mutation UpdateDriverLocation($latitude: Float!, $longitude: Float!) {
    updateDriverLocation(latitude: $latitude, longitude: $longitude) {
      success
      message
    }
  }
`;

export const SET_DRIVER_AVAILABILITY_MUTATION = `
  mutation SetDriverAvailability($isAvailable: Boolean!) {
    setDriverAvailability(isAvailable: $isAvailable) {
      success
      message
      isAvailable
    }
  }
`;

export const UPDATE_DRIVER_FCM_TOKEN_MUTATION = `
  mutation UpdateDriverFCMToken($token: String!) {
    updateDriverFCMToken(token: $token) {
      success
      message
    }
  }
`;

export const ACCEPT_TRIP_MUTATION = `
  mutation AcceptTrip($tripId: ID!) {
    acceptTrip(tripId: $tripId) {
      success
      message
      trip {
        id
        status
        pickup_address
        destination_address
        estimated_fare
        estimated_distance
        estimated_duration
        user {
          id
          first_name
          last_name
          mobile
        }
      }
    }
  }
`;

export const REJECT_TRIP_MUTATION = `
  mutation RejectTrip($tripId: ID!) {
    rejectTrip(tripId: $tripId) {
      success
      message
      trip {
        id
        status
      }
    }
  }
`;

export const UPDATE_TRIP_STATUS_MUTATION = `
  mutation UpdateTripStatus($tripId: ID!, $status: TripStatus!) {
    updateTripStatus(tripId: $tripId, status: $status) {
      success
      message
      trip {
        id
        status
        pickup_address
        destination_address
        user {
          id
          first_name
          last_name
          mobile
        }
      }
    }
  }
`;

export const GET_DRIVER_TRIPS_QUERY = `
  query GetDriverTrips($limit: Int, $offset: Int) {
    getDriverTrips(limit: $limit, offset: $offset) {
      id
      status
      pickup_address
      destination_address
      estimated_fare
      estimated_distance
      estimated_duration
      requested_at
      completed_at
      user {
        id
        first_name
        last_name
        mobile
      }
    }
  }
`;

export const ME_QUERY = `
  query DriverMe {
    driverMe {
      id
      first_name
      last_name
      mobile
      full_name
      mobile_verified_at
      created_at
    }
  }
`;

// Types
export interface Driver {
  id: string;
  first_name: string;
  last_name: string;
  mobile: string;
  full_name: string;
  mobile_verified_at?: string;
  created_at: string;
}

export interface DriverSignupInput {
  first_name: string;
  last_name: string;
  mobile: string;
}

export interface DriverSigninInput {
  mobile: string;
}

export interface VerifyOtpInput {
  mobile: string;
  otp: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
}

export interface AuthTokenResponse extends AuthResponse {
  token?: string;
  user?: Driver;
}
