import { createDrawerNavigator } from '@react-navigation/drawer';
import { NavigationContainer } from '@react-navigation/native';
import HomeScreen from './home';
import DrawerContent from '../../components/DrawerContent';

const Drawer = createDrawerNavigator();

export default function MainLayout() {
  return (
    <Drawer.Navigator
      drawerContent={() => <DrawerContent />}
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          width: 280,
        },
      }}
    >
      <Drawer.Screen name="home" component={HomeScreen} />
    </Drawer.Navigator>
  );
}
