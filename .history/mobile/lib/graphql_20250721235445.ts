import { createClient, cacheExchange, fetchExchange } from 'urql';
import AsyncStorage from '@react-native-async-storage/async-storage';

const API_URL = 'http://127.0.0.1:8000/graphql';

// Create the GraphQL client
export const graphqlClient = createClient({
  url: API_URL,
  exchanges: [cacheExchange, fetchExchange],
  fetchOptions: async () => {
    const token = await AsyncStorage.getItem('auth_token');
    return {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    };
  },
});

// GraphQL mutations and queries
export const SIGNUP_MUTATION = `
  mutation Signup($input: SignupInput!) {
    signup(input: $input) {
      success
      message
    }
  }
`;

export const SIGNIN_MUTATION = `
  mutation Signin($input: SigninInput!) {
    signin(input: $input) {
      success
      message
    }
  }
`;

export const VERIFY_OTP_MUTATION = `
  mutation VerifyOtp($input: VerifyOtpInput!) {
    verifyOtp(input: $input) {
      success
      message
      token
      user {
        id
        first_name
        last_name
        mobile
        full_name
      }
    }
  }
`;

export const ME_QUERY = `
  query Me {
    me {
      id
      first_name
      last_name
      mobile
      full_name
      mobile_verified_at
      created_at
    }
  }
`;

// Types
export interface User {
  id: string;
  first_name: string;
  last_name: string;
  mobile: string;
  full_name: string;
  mobile_verified_at?: string;
  created_at: string;
}

export interface SignupInput {
  first_name: string;
  last_name: string;
  mobile: string;
}

export interface SigninInput {
  mobile: string;
}

export interface VerifyOtpInput {
  mobile: string;
  otp: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
}

export interface AuthTokenResponse extends AuthResponse {
  token?: string;
  user?: User;
}
