"A datetime string with format `Y-m-d H:i:s`, e.g. `2018-05-23 13:43:32`."
scalar DateTime @scalar(class: "Nuwave\\Lighthouse\\Schema\\Types\\Scalars\\DateTime")

"Indicates what fields are available at the top level of a query operation."
type Query {
    "Get current authenticated user."
    userMe: User @field(resolver: "App\\GraphQL\\Queries\\UserQueries@me")

    "Get current authenticated driver."
    driverMe: Driver @field(resolver: "App\\GraphQL\\Queries\\DriverQueries@me")
}

"Available mutations for the application."
type Mutation {
    "Register a new user with mobile number."
    userSignup(input: SignupInput! @spread): AuthResponse! @field(resolver: "App\\GraphQL\\Mutations\\AuthMutations@userSignup")

    "Sign in with mobile number."
    userSignin(input: SigninInput! @spread): AuthResponse! @field(resolver: "App\\GraphQL\\Mutations\\AuthMutations@userSignin")

    "Verify OTP and get authentication token."
    userVerifyOtp(input: VerifyOtpInput! @spread): AuthTokenResponse! @field(resolver: "App\\GraphQL\\Mutations\\AuthMutations@userVerifyOtp")

    "Register a new driver with mobile number."
    driverSignup(input: DriverSignupInput! @spread): AuthResponse! @field(resolver: "App\\GraphQL\\Mutations\\DriverAuthMutations@driverSignup")

    "Sign in driver with mobile number."
    driverSignin(input: DriverSigninInput! @spread): AuthResponse! @field(resolver: "App\\GraphQL\\Mutations\\DriverAuthMutations@driverSignin")

    "Verify driver OTP and get authentication token."
    driverVerifyOtp(input: VerifyOtpInput! @spread): AuthTokenResponse! @field(resolver: "App\\GraphQL\\Mutations\\DriverAuthMutations@driverVerifyOtp")
}

"Input for user registration."
input SignupInput {
    "User's first name."
    first_name: String! @rules(apply: ["required", "string", "max:255"])

    "User's last name."
    last_name: String! @rules(apply: ["required", "string", "max:255"])

    "User's mobile number."
    mobile: String! @rules(apply: ["required", "string", "regex:/^[+]?[0-9]{10,15}$/", "unique:users,mobile"])
}

"Input for user sign in."
input SigninInput {
    "User's mobile number."
    mobile: String! @rules(apply: ["required", "string", "exists:users,mobile"])
}


"Input for driver registration."
input DriverSignupInput {
    "driver's first name."
    first_name: String! @rules(apply: ["required", "string", "max:255"])

    "driver's last name."
    last_name: String! @rules(apply: ["required", "string", "max:255"])

    "driver's mobile number."
    mobile: String! @rules(apply: ["required", "string", "regex:/^[+]?[0-9]{10,15}$/", "unique:drivers,mobile"])
}

"Input for driver sign in."
input DriverSigninInput {
    "driver's mobile number."
    mobile: String! @rules(apply: ["required", "string", "exists:drivers,mobile"])
}

"Input for OTP verification."
input VerifyOtpInput {
    "User's mobile number."
    mobile: String! @rules(apply: ["required", "string"])

    "OTP code."
    otp: String! @rules(apply: ["required", "string", "size:6"])
}

"Response for signup and signin operations."
type AuthResponse {
    "Success status."
    success: Boolean!

    "Response message."
    message: String!
}

"Response for OTP verification with token."
type AuthTokenResponse {
    "Success status."
    success: Boolean!

    "Response message."
    message: String!

    "Authentication token (only on success)."
    token: String

    "User data (only on success)."
    user: User
}

"Account of a person who uses this application."
type User {
    "Unique primary key."
    id: ID!

    "User's first name."
    first_name: String!

    "User's last name."
    last_name: String!

    "User's full name."
    full_name: String!

    "User's mobile number."
    mobile: String!

    "User's email address."
    email: String

    "When the mobile was verified."
    mobile_verified_at: DateTime

    "When the email was verified."
    email_verified_at: DateTime

    "When the account was created."
    created_at: DateTime!

    "When the account was last updated."
    updated_at: DateTime!
}

"Account of a driver who provides rides."
type Driver {
    "Unique primary key."
    id: ID!

    "Driver's first name."
    first_name: String!

    "Driver's last name."
    last_name: String!

    "Driver's full name."
    full_name: String!

    "Driver's mobile number."
    mobile: String!

    "Driver's email address."
    email: String

    "When the mobile was verified."
    mobile_verified_at: DateTime

    "When the email was verified."
    email_verified_at: DateTime

    "When the account was created."
    created_at: DateTime!

    "When the account was last updated."
    updated_at: DateTime!
}
