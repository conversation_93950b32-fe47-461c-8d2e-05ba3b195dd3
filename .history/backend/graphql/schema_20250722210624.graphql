"A datetime string with format `Y-m-d H:i:s`, e.g. `2018-05-23 13:43:32`."
scalar DateTime @scalar(class: "Nuwave\\Lighthouse\\Schema\\Types\\Scalars\\DateTime")

"Indicates what fields are available at the top level of a query operation."
type Query {
    "Get current authenticated user."
    userMe: User @field(resolver: "App\\GraphQL\\Queries\\UserQueries@me")

    "Get current authenticated driver."
    driverMe: Driver @field(resolver: "App\\GraphQL\\Queries\\DriverQueries@me")

    "Get nearby drivers for a location."
    getNearbyDrivers(latitude: Float!, longitude: Float!, radius: Float = 5.0): [Driver!]! @field(resolver: "App\\GraphQL\\Queries\\TripQueries@getNearbyDrivers")

    "Get user's trips."
    getUserTrips(limit: Int = 20, offset: Int = 0): [Trip!]! @field(resolver: "App\\GraphQL\\Queries\\TripQueries@getUserTrips")

    "Get driver's trips."
    getDriverTrips(limit: Int = 20, offset: Int = 0): [Trip!]! @field(resolver: "App\\GraphQL\\Queries\\TripQueries@getDriverTrips")

    "Get current active trip."
    getCurrentTrip: Trip @field(resolver: "App\\GraphQL\\Queries\\TripQueries@getCurrentTrip")

    "Get trip details by ID."
    getTripDetails(tripId: ID!): Trip @field(resolver: "App\\GraphQL\\Queries\\TripQueries@getTripDetails")
}

"Available mutations for the application."
type Mutation {
    "Register a new user with mobile number."
    userSignup(input: SignupInput! @spread): AuthResponse! @field(resolver: "App\\GraphQL\\Mutations\\AuthMutations@userSignup")

    "Sign in with mobile number."
    userSignin(input: SigninInput! @spread): AuthResponse! @field(resolver: "App\\GraphQL\\Mutations\\AuthMutations@userSignin")

    "Verify OTP and get authentication token."
    userVerifyOtp(input: VerifyOtpInput! @spread): AuthTokenResponse! @field(resolver: "App\\GraphQL\\Mutations\\AuthMutations@userVerifyOtp")

    "Register a new driver with mobile number."
    driverSignup(input: DriverSignupInput! @spread): AuthResponse! @field(resolver: "App\\GraphQL\\Mutations\\DriverAuthMutations@driverSignup")

    "Sign in driver with mobile number."
    driverSignin(input: DriverSigninInput! @spread): AuthResponse! @field(resolver: "App\\GraphQL\\Mutations\\DriverAuthMutations@driverSignin")

    "Verify driver OTP and get authentication token."
    driverVerifyOtp(input: VerifyOtpInput! @spread): AuthTokenResponse! @field(resolver: "App\\GraphQL\\Mutations\\DriverAuthMutations@driverVerifyOtp")

    "Update user location."
    updateUserLocation(latitude: Float!, longitude: Float!): LocationUpdateResponse! @field(resolver: "App\\GraphQL\\Mutations\\LocationMutations@updateUserLocation")

    "Update driver location."
    updateDriverLocation(latitude: Float!, longitude: Float!): LocationUpdateResponse! @field(resolver: "App\\GraphQL\\Mutations\\LocationMutations@updateDriverLocation")

    "Set driver availability status."
    setDriverAvailability(isAvailable: Boolean!): DriverStatusResponse! @field(resolver: "App\\GraphQL\\Mutations\\LocationMutations@setDriverAvailability")

    "Update FCM token for user."
    updateUserFCMToken(token: String!): TokenUpdateResponse! @field(resolver: "App\\GraphQL\\Mutations\\LocationMutations@updateUserFCMToken")

    "Update FCM token for driver."
    updateDriverFCMToken(token: String!): TokenUpdateResponse! @field(resolver: "App\\GraphQL\\Mutations\\LocationMutations@updateDriverFCMToken")

    "Request a new trip."
    requestTrip(input: TripRequestInput! @spread): TripRequestResponse! @field(resolver: "App\\GraphQL\\Mutations\\TripMutations@requestTrip")

    "Accept a trip request."
    acceptTrip(tripId: ID!): TripActionResponse! @field(resolver: "App\\GraphQL\\Mutations\\TripMutations@acceptTrip")

    "Reject a trip request."
    rejectTrip(tripId: ID!): TripActionResponse! @field(resolver: "App\\GraphQL\\Mutations\\TripMutations@rejectTrip")

    "Update trip status."
    updateTripStatus(tripId: ID!, status: TripStatus!): TripStatusResponse! @field(resolver: "App\\GraphQL\\Mutations\\TripMutations@updateTripStatus")

    "Cancel a trip."
    cancelTrip(tripId: ID!, reason: String): TripCancellationResponse! @field(resolver: "App\\GraphQL\\Mutations\\TripMutations@cancelTrip")
}

"Input for user registration."
input SignupInput {
    "User's first name."
    first_name: String! @rules(apply: ["required", "string", "max:255"])

    "User's last name."
    last_name: String! @rules(apply: ["required", "string", "max:255"])

    "User's mobile number."
    mobile: String! @rules(apply: ["required", "string", "regex:/^[+]?[0-9]{10,15}$/", "unique:users,mobile"])
}

"Input for user sign in."
input SigninInput {
    "User's mobile number."
    mobile: String! @rules(apply: ["required", "string", "exists:users,mobile"])
}


"Input for driver registration."
input DriverSignupInput {
    "driver's first name."
    first_name: String! @rules(apply: ["required", "string", "max:255"])

    "driver's last name."
    last_name: String! @rules(apply: ["required", "string", "max:255"])

    "driver's mobile number."
    mobile: String! @rules(apply: ["required", "string", "regex:/^[+]?[0-9]{10,15}$/", "unique:drivers,mobile"])
}

"Input for driver sign in."
input DriverSigninInput {
    "driver's mobile number."
    mobile: String! @rules(apply: ["required", "string", "exists:drivers,mobile"])
}

"Input for OTP verification."
input VerifyOtpInput {
    "User's mobile number."
    mobile: String! @rules(apply: ["required", "string"])

    "OTP code."
    otp: String! @rules(apply: ["required", "string", "size:6"])
}

"Response for signup and signin operations."
type AuthResponse {
    "Success status."
    success: Boolean!

    "Response message."
    message: String!
}

"Response for OTP verification with token."
type AuthTokenResponse {
    "Success status."
    success: Boolean!

    "Response message."
    message: String!

    "Authentication token (only on success)."
    token: String

    "User data (only on success)."
    user: User
}

"Response for location update operations."
type LocationUpdateResponse {
    "Success status."
    success: Boolean!

    "Response message."
    message: String!
}

"Response for driver status operations."
type DriverStatusResponse {
    "Success status."
    success: Boolean!

    "Response message."
    message: String!

    "Current availability status."
    isAvailable: Boolean!
}

"Response for FCM token update operations."
type TokenUpdateResponse {
    "Success status."
    success: Boolean!

    "Response message."
    message: String!
}

"Account of a person who uses this application."
type User {
    "Unique primary key."
    id: ID!

    "User's first name."
    first_name: String!

    "User's last name."
    last_name: String!

    "User's full name."
    full_name: String!

    "User's mobile number."
    mobile: String!

    "User's email address."
    email: String

    "When the mobile was verified."
    mobile_verified_at: DateTime

    "When the email was verified."
    email_verified_at: DateTime

    "When the account was created."
    created_at: DateTime!

    "When the account was last updated."
    updated_at: DateTime!
}

"Account of a driver who provides rides."
type Driver {
    "Unique primary key."
    id: ID!

    "Driver's first name."
    first_name: String!

    "Driver's last name."
    last_name: String!

    "Driver's full name."
    full_name: String!

    "Driver's mobile number."
    mobile: String!

    "Driver's email address."
    email: String

    "When the mobile was verified."
    mobile_verified_at: DateTime

    "When the email was verified."
    email_verified_at: DateTime

    "When the account was created."
    created_at: DateTime!

    "When the account was last updated."
    updated_at: DateTime!
}

"Trip data type."
type Trip {
    "Unique primary key."
    id: ID!

    "User who requested the trip."
    user: User!

    "Driver assigned to the trip."
    driver: Driver

    "Pickup location latitude."
    pickup_latitude: Float!

    "Pickup location longitude."
    pickup_longitude: Float!

    "Pickup address."
    pickup_address: String!

    "Destination latitude."
    destination_latitude: Float!

    "Destination longitude."
    destination_longitude: Float!

    "Destination address."
    destination_address: String!

    "Trip status."
    status: TripStatus!

    "When the trip was requested."
    requested_at: DateTime!

    "When the trip was accepted."
    accepted_at: DateTime

    "When the driver picked up the user."
    picked_up_at: DateTime

    "When the trip started."
    started_at: DateTime

    "When the trip was completed."
    completed_at: DateTime

    "Estimated fare."
    estimated_fare: Float!

    "Actual fare."
    actual_fare: Float

    "Estimated duration in minutes."
    estimated_duration: Int!

    "Estimated distance in km."
    estimated_distance: Float!

    "Number of rejections."
    rejection_count: Int!

    "When the trip was created."
    created_at: DateTime!

    "When the trip was last updated."
    updated_at: DateTime!
}

"Input for trip request."
input TripRequestInput {
    "Pickup address."
    pickupAddress: String!

    "Pickup latitude."
    pickupLat: Float!

    "Pickup longitude."
    pickupLng: Float!

    "Destination address."
    destinationAddress: String!

    "Destination latitude."
    destinationLat: Float!

    "Destination longitude."
    destinationLng: Float!
}

"Trip status enum."
enum TripStatus {
    REQUESTED
    DRIVER_ASSIGNED
    DRIVER_ARRIVING
    DRIVER_ARRIVED
    TRIP_STARTED
    TRIP_COMPLETED
    CANCELLED_BY_USER
    CANCELLED_BY_DRIVER
}

"Response for trip request."
type TripRequestResponse {
    "Success status."
    success: Boolean!

    "Response message."
    message: String!

    "Trip data (only on success)."
    trip: Trip
}

"Response for trip actions."
type TripActionResponse {
    "Success status."
    success: Boolean!

    "Response message."
    message: String!

    "Trip data."
    trip: Trip
}

"Response for trip status updates."
type TripStatusResponse {
    "Success status."
    success: Boolean!

    "Response message."
    message: String!

    "Updated trip data."
    trip: Trip
}

"Response for trip cancellation."
type TripCancellationResponse {
    "Success status."
    success: Boolean!

    "Response message."
    message: String!

    "Cancellation reason."
    reason: String
}
