<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trips', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('driver_id')->nullable()->constrained()->onDelete('set null');

            // Pickup location
            $table->decimal('pickup_latitude', 10, 8);
            $table->decimal('pickup_longitude', 11, 8);
            $table->text('pickup_address');

            // Destination location
            $table->decimal('destination_latitude', 10, 8);
            $table->decimal('destination_longitude', 11, 8);
            $table->text('destination_address');

            // Trip status and timing
            $table->enum('status', [
                'requested',
                'driver_assigned',
                'driver_arriving',
                'driver_arrived',
                'trip_started',
                'trip_completed',
                'cancelled_by_user',
                'cancelled_by_driver'
            ])->default('requested');

            $table->timestamp('requested_at');
            $table->timestamp('accepted_at')->nullable();
            $table->timestamp('picked_up_at')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();

            // Trip details
            $table->decimal('estimated_fare', 8, 2);
            $table->decimal('actual_fare', 8, 2)->nullable();
            $table->integer('estimated_duration'); // minutes
            $table->decimal('estimated_distance', 6, 2); // km
            $table->integer('rejection_count')->default(0);

            $table->timestamps();

            // Indexes for performance
            $table->index(['user_id', 'status']);
            $table->index(['driver_id', 'status']);
            $table->index(['status', 'requested_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('trips');
    }
};
