<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trip_notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('trip_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('driver_id')->nullable()->constrained()->onDelete('cascade');

            $table->enum('notification_type', [
                'trip_request',
                'trip_accepted',
                'driver_arriving',
                'driver_arrived',
                'trip_started',
                'trip_completed',
                'trip_cancelled'
            ]);

            $table->text('message');
            $table->timestamp('sent_at');
            $table->timestamp('read_at')->nullable();
            $table->string('fcm_message_id')->nullable();

            $table->timestamps();

            // Indexes for performance
            $table->index(['trip_id', 'notification_type']);
            $table->index(['user_id', 'sent_at']);
            $table->index(['driver_id', 'sent_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('trip_notifications');
    }
};
