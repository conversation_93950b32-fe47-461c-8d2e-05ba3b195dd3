<?php

namespace App\GraphQL\Queries;

use App\Models\Trip;
use App\Models\Driver;
use App\Services\DriverMatchingService;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class TripQueries
{
    protected $driverMatchingService;

    public function __construct(DriverMatchingService $driverMatchingService)
    {
        $this->driverMatchingService = $driverMatchingService;
    }

    /**
     * Get nearby drivers
     */
    public function getNearbyDrivers($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        return $this->driverMatchingService->findNearbyDrivers(
            $args['latitude'],
            $args['longitude'],
            $args['radius'] ?? 5.0
        );
    }

    /**
     * Get user's trips
     */
    public function getUserTrips($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $user = $context->user();
        if (!$user) {
            return [];
        }

        return Trip::where('user_id', $user->id)
            ->with(['driver'])
            ->orderBy('created_at', 'desc')
            ->limit($args['limit'] ?? 20)
            ->offset($args['offset'] ?? 0)
            ->get();
    }

    /**
     * Get driver's trips
     */
    public function getDriverTrips($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        // Get the driver from the token
        $request = $context->request();
        $token = $request->bearerToken();

        if (!$token) {
            return [];
        }

        $tokenModel = \Laravel\Sanctum\PersonalAccessToken::findToken($token);
        if (!$tokenModel || !$tokenModel->tokenable instanceof Driver) {
            return [];
        }

        $driver = $tokenModel->tokenable;

        return Trip::where('driver_id', $driver->id)
            ->with(['user'])
            ->orderBy('created_at', 'desc')
            ->limit($args['limit'] ?? 20)
            ->offset($args['offset'] ?? 0)
            ->get();
    }

    /**
     * Get current active trip
     */
    public function getCurrentTrip($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $user = $context->user();
        if (!$user) {
            return null;
        }

        return Trip::where('user_id', $user->id)
            ->whereIn('status', ['requested', 'driver_assigned', 'driver_arriving', 'driver_arrived', 'trip_started'])
            ->with(['driver'])
            ->first();
    }

    /**
     * Get trip details by ID
     */
    public function getTripDetails($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        $user = $context->user();
        if (!$user) {
            return null;
        }

        return Trip::where('id', $args['tripId'])
            ->where('user_id', $user->id)
            ->with(['driver'])
            ->first();
    }
}
