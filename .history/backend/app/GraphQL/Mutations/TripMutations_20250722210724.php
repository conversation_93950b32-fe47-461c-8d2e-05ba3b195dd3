<?php

namespace App\GraphQL\Mutations;

use App\Models\Trip;
use App\Models\User;
use App\Models\Driver;
use App\Services\FCMNotificationService;
use App\Services\DriverMatchingService;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class TripMutations
{
    protected $fcmService;
    protected $driverMatchingService;

    public function __construct(FCMNotificationService $fcmService, DriverMatchingService $driverMatchingService)
    {
        $this->fcmService = $fcmService;
        $this->driverMatchingService = $driverMatchingService;
    }

    /**
     * Request a new trip
     */
    public function requestTrip($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): array
    {
        try {
            $user = $context->user();
            if (!$user) {
                return [
                    'success' => false,
                    'message' => 'User not authenticated.',
                    'trip' => null,
                ];
            }

            // Check if user has an active trip
            $activeTrip = Trip::where('user_id', $user->id)
                ->whereIn('status', ['requested', 'driver_assigned', 'driver_arriving', 'driver_arrived', 'trip_started'])
                ->first();

            if ($activeTrip) {
                return [
                    'success' => false,
                    'message' => 'You already have an active trip.',
                    'trip' => null,
                ];
            }

            // Calculate estimated fare and distance
            $distance = Trip::calculateDistance(
                $args['pickupLat'],
                $args['pickupLng'],
                $args['destinationLat'],
                $args['destinationLng']
            );

            $estimatedFare = $this->calculateFare($distance);
            $estimatedDuration = $this->calculateDuration($distance);

            // Create the trip
            $trip = Trip::create([
                'user_id' => $user->id,
                'pickup_latitude' => $args['pickupLat'],
                'pickup_longitude' => $args['pickupLng'],
                'pickup_address' => $args['pickupAddress'],
                'destination_latitude' => $args['destinationLat'],
                'destination_longitude' => $args['destinationLng'],
                'destination_address' => $args['destinationAddress'],
                'status' => 'requested',
                'requested_at' => now(),
                'estimated_fare' => $estimatedFare,
                'estimated_duration' => $estimatedDuration,
                'estimated_distance' => $distance,
            ]);

            // Find and notify nearby drivers
            $this->driverMatchingService->findAndNotifyDrivers($trip);

            return [
                'success' => true,
                'message' => 'Trip requested successfully. Searching for nearby drivers...',
                'trip' => $trip->load('user'),
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to request trip: ' . $e->getMessage(),
                'trip' => null,
            ];
        }
    }

    /**
     * Accept a trip request
     */
    public function acceptTrip($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): array
    {
        try {
            // Get the driver from the token
            $request = $context->request();
            $token = $request->bearerToken();

            if (!$token) {
                return [
                    'success' => false,
                    'message' => 'Driver not authenticated.',
                    'trip' => null,
                ];
            }

            $tokenModel = \Laravel\Sanctum\PersonalAccessToken::findToken($token);
            if (!$tokenModel || !$tokenModel->tokenable instanceof Driver) {
                return [
                    'success' => false,
                    'message' => 'Invalid driver token.',
                    'trip' => null,
                ];
            }

            $driver = $tokenModel->tokenable;

            // Check if driver is available
            if (!$driver->is_available) {
                return [
                    'success' => false,
                    'message' => 'Driver is not available.',
                    'trip' => null,
                ];
            }

            // Find the trip
            $trip = Trip::find($args['tripId']);
            if (!$trip) {
                return [
                    'success' => false,
                    'message' => 'Trip not found.',
                    'trip' => null,
                ];
            }

            // Check if trip is still available
            if ($trip->status !== 'requested') {
                return [
                    'success' => false,
                    'message' => 'Trip is no longer available.',
                    'trip' => null,
                ];
            }

            // Assign driver to trip
            $trip->update([
                'driver_id' => $driver->id,
                'status' => 'driver_assigned',
                'accepted_at' => now(),
            ]);

            // Set driver as unavailable
            $driver->update(['is_available' => false]);

            // Send notification to user
            $this->fcmService->sendDriverAssignedNotification($trip->load(['user', 'driver']));

            return [
                'success' => true,
                'message' => 'Trip accepted successfully.',
                'trip' => $trip->load(['user', 'driver']),
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to accept trip: ' . $e->getMessage(),
                'trip' => null,
            ];
        }
    }
