<?php

namespace App\GraphQL\Mutations;

use App\Models\User;
use App\Models\Driver;
use App\Services\FirebaseService;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class LocationMutations
{
    protected $firebaseService;

    public function __construct(FirebaseService $firebaseService)
    {
        $this->firebaseService = $firebaseService;
    }

    /**
     * Update user location
     */
    public function updateUserLocation($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): array
    {
        try {
            $user = $context->user();
            if (!$user) {
                return [
                    'success' => false,
                    'message' => 'User not authenticated.',
                ];
            }

            // Validate coordinates
            if (!$this->isValidCoordinate($args['latitude'], $args['longitude'])) {
                return [
                    'success' => false,
                    'message' => 'Invalid coordinates provided.',
                ];
            }

            $user->update([
                'current_latitude' => $args['latitude'],
                'current_longitude' => $args['longitude'],
                'last_location_update' => now(),
            ]);

            return [
                'success' => true,
                'message' => 'Location updated successfully.',
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to update location: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Update driver location
     */
    public function updateDriverLocation($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): array
    {
        try {
            // Get the driver from the token
            $request = $context->request();
            $token = $request->bearerToken();

            if (!$token) {
                return [
                    'success' => false,
                    'message' => 'Driver not authenticated.',
                ];
            }

            $tokenModel = \Laravel\Sanctum\PersonalAccessToken::findToken($token);
            if (!$tokenModel || !$tokenModel->tokenable instanceof Driver) {
                return [
                    'success' => false,
                    'message' => 'Invalid driver token.',
                ];
            }

            $driver = $tokenModel->tokenable;

            // Validate coordinates
            if (!$this->isValidCoordinate($args['latitude'], $args['longitude'])) {
                return [
                    'success' => false,
                    'message' => 'Invalid coordinates provided.',
                ];
            }

            $driver->update([
                'current_latitude' => $args['latitude'],
                'current_longitude' => $args['longitude'],
                'last_location_update' => now(),
            ]);

            return [
                'success' => true,
                'message' => 'Driver location updated successfully.',
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to update driver location: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Set driver availability
     */
    public function setDriverAvailability($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): array
    {
        try {
            // Get the driver from the token
            $request = $context->request();
            $token = $request->bearerToken();

            if (!$token) {
                return [
                    'success' => false,
                    'message' => 'Driver not authenticated.',
                    'isAvailable' => false,
                ];
            }

            $tokenModel = \Laravel\Sanctum\PersonalAccessToken::findToken($token);
            if (!$tokenModel || !$tokenModel->tokenable instanceof Driver) {
                return [
                    'success' => false,
                    'message' => 'Invalid driver token.',
                    'isAvailable' => false,
                ];
            }

            $driver = $tokenModel->tokenable;
            $isAvailable = $args['isAvailable'];

            $driver->update([
                'is_available' => $isAvailable,
                'is_online' => $isAvailable, // If not available, also set offline
            ]);

            return [
                'success' => true,
                'message' => $isAvailable ? 'Driver is now available for trips.' : 'Driver is now unavailable.',
                'isAvailable' => $isAvailable,
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to update driver availability: ' . $e->getMessage(),
                'isAvailable' => false,
            ];
        }
    }

    /**
     * Update user FCM token
     */
    public function updateUserFCMToken($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): array
    {
        try {
            $user = $context->user();
            if (!$user) {
                return [
                    'success' => false,
                    'message' => 'User not authenticated.',
                ];
            }

            $token = $args['token'];
            // Temporarily disable FCM token validation for testing
            // if (!$this->firebaseService->isValidToken($token)) {
            //     return [
            //         'success' => false,
            //         'message' => 'Invalid FCM token format.',
            //     ];
            // }

            $user->update(['fcm_token' => $token]);

            return [
                'success' => true,
                'message' => 'FCM token updated successfully.',
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to update FCM token: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Update driver FCM token
     */
    public function updateDriverFCMToken($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): array
    {
        try {
            // Get the driver from the token
            $request = $context->request();
            $authToken = $request->bearerToken();

            if (!$authToken) {
                return [
                    'success' => false,
                    'message' => 'Driver not authenticated.',
                ];
            }

            $tokenModel = \Laravel\Sanctum\PersonalAccessToken::findToken($authToken);
            if (!$tokenModel || !$tokenModel->tokenable instanceof Driver) {
                return [
                    'success' => false,
                    'message' => 'Invalid driver token.',
                ];
            }

            $driver = $tokenModel->tokenable;
            $fcmToken = $args['token'];

            // Temporarily disable FCM token validation for testing
            // if (!$this->firebaseService->isValidToken($fcmToken)) {
            //     return [
            //         'success' => false,
            //         'message' => 'Invalid FCM token format.',
            //     ];
            // }

            $driver->update(['fcm_token' => $fcmToken]);

            return [
                'success' => true,
                'message' => 'Driver FCM token updated successfully.',
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to update driver FCM token: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Validate coordinate values
     */
    private function isValidCoordinate(float $latitude, float $longitude): bool
    {
        return $latitude >= -90 && $latitude <= 90 && $longitude >= -180 && $longitude <= 180;
    }
}
