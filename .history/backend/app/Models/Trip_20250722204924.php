<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Trip extends Model
{
    protected $fillable = [
        'user_id',
        'driver_id',
        'pickup_latitude',
        'pickup_longitude',
        'pickup_address',
        'destination_latitude',
        'destination_longitude',
        'destination_address',
        'status',
        'requested_at',
        'accepted_at',
        'picked_up_at',
        'started_at',
        'completed_at',
        'estimated_fare',
        'actual_fare',
        'estimated_duration',
        'estimated_distance',
        'rejection_count',
    ];

    protected $casts = [
        'pickup_latitude' => 'decimal:8',
        'pickup_longitude' => 'decimal:8',
        'destination_latitude' => 'decimal:8',
        'destination_longitude' => 'decimal:8',
        'estimated_fare' => 'decimal:2',
        'actual_fare' => 'decimal:2',
        'estimated_distance' => 'decimal:2',
        'requested_at' => 'datetime',
        'accepted_at' => 'datetime',
        'picked_up_at' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * Get the user that owns the trip
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the driver assigned to the trip
     */
    public function driver(): BelongsTo
    {
        return $this->belongsTo(Driver::class);
    }

    /**
     * Check if trip is active (not completed or cancelled)
     */
    public function isActive(): bool
    {
        return !in_array($this->status, ['trip_completed', 'cancelled_by_user', 'cancelled_by_driver']);
    }

    /**
     * Check if trip can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['requested', 'driver_assigned', 'driver_arriving']);
    }

    /**
     * Calculate distance between two points using Haversine formula
     */
    public static function calculateDistance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));

        return $earthRadius * $c;
    }
}
