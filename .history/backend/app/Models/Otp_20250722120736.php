<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Otp extends Model
{
    protected $fillable = [
        'mobile',
        'otp',
        'type',
        'user_type',
        'expires_at',
        'is_used',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'is_used' => 'boolean',
    ];

    /**
     * Generate a new OTP for the given mobile number
     */
    public static function generateOtp(string $mobile, string $type = 'signin', string $userType = 'user'): string
    {
        // Generate 6-digit OTP
        $otp = str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);

        // Mark any existing unused OTPs as used for this mobile and user type
        self::where('mobile', $mobile)
            ->where('user_type', $userType)
            ->where('is_used', false)
            ->update(['is_used' => true]);

        // Create new OTP
        self::create([
            'mobile' => $mobile,
            'otp' => $otp,
            'type' => $type,
            'user_type' => $userType,
            'expires_at' => Carbon::now()->addMinutes(5), // 5 minutes expiry
            'is_used' => false,
        ]);

        return $otp;
    }

    /**
     * Verify OTP for the given mobile number
     */
    public static function verifyOtp(string $mobile, string $otp, string $userType = 'user'): bool
    {
        $otpRecord = self::where('mobile', $mobile)
            ->where('otp', $otp)
            ->where('user_type', $userType)
            ->where('is_used', false)
            ->where('expires_at', '>', Carbon::now())
            ->first();

        if ($otpRecord) {
            $otpRecord->update(['is_used' => true]);
            return true;
        }

        return false;
    }

    /**
     * Clean up expired OTPs
     */
    public static function cleanupExpired(): void
    {
        self::where('expires_at', '<', Carbon::now())->delete();
    }
}
