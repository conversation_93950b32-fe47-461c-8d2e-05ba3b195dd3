<?php

namespace App\Services;

use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;
use Kreait\Firebase\Exception\MessagingException;
use Illuminate\Support\Facades\Log;

class FirebaseService
{
    private $messaging;

    public function __construct()
    {
        try {
            $serviceAccountPath = storage_path('app/firebase/service-account.json');

            if (!file_exists($serviceAccountPath)) {
                Log::error('Firebase service account file not found at: ' . $serviceAccountPath);
                return;
            }

            $factory = (new Factory)->withServiceAccount($serviceAccountPath);
            $this->messaging = $factory->createMessaging();
        } catch (\Exception $e) {
            Log::error('Failed to initialize Firebase: ' . $e->getMessage());
        }
    }

    /**
     * Send FCM notification to a specific device token
     */
    public function sendNotification(string $token, string $title, string $body, array $data = []): ?string
    {
        if (!$this->messaging) {
            Log::error('Firebase messaging not initialized');
            return null;
        }

        try {
            $notification = Notification::create($title, $body);

            $message = CloudMessage::new()
                ->toToken($token)
                ->withNotification($notification)
                ->withData($data);

            $result = $this->messaging->send($message);

            Log::info('FCM notification sent successfully', [
                'token' => substr($token, 0, 20) . '...',
                'title' => $title,
                'message_id' => $result
            ]);

            return $result;
        } catch (MessagingException $e) {
            Log::error('FCM messaging error: ' . $e->getMessage(), [
                'token' => substr($token, 0, 20) . '...',
                'title' => $title
            ]);
            return null;
        } catch (\Exception $e) {
            Log::error('Unexpected error sending FCM notification: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Send notification to multiple tokens
     */
    public function sendMulticastNotification(array $tokens, string $title, string $body, array $data = []): array
    {
        if (!$this->messaging || empty($tokens)) {
            return [];
        }

        try {
            $notification = Notification::create($title, $body);

            $message = CloudMessage::new()
                ->withNotification($notification)
                ->withData($data);

            $result = $this->messaging->sendMulticast($message, $tokens);

            Log::info('FCM multicast notification sent', [
                'token_count' => count($tokens),
                'success_count' => $result->successes()->count(),
                'failure_count' => $result->failures()->count()
            ]);

            return [
                'success_count' => $result->successes()->count(),
                'failure_count' => $result->failures()->count(),
                'failures' => $result->failures()->getItems()
            ];
        } catch (\Exception $e) {
            Log::error('Error sending multicast FCM notification: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Validate FCM token format
     */
    public function isValidToken(string $token): bool
    {
        // Basic validation - FCM tokens are typically 152+ characters
        return strlen($token) >= 140 && preg_match('/^[a-zA-Z0-9_-]+$/', str_replace(':', '', $token));
    }
}
