<?php

namespace App\Services;

use App\Models\Trip;
use App\Models\TripNotification;
use App\Models\User;
use App\Models\Driver;
use Illuminate\Support\Facades\Log;

class FCMNotificationService
{
    protected $firebaseService;

    public function __construct(FirebaseService $firebaseService)
    {
        $this->firebaseService = $firebaseService;
    }

    /**
     * Send trip request notification to driver
     */
    public function sendTripRequestNotification(Trip $trip, Driver $driver): bool
    {
        if (!$driver->fcm_token) {
            Log::warning('Driver has no FCM token', ['driver_id' => $driver->id]);
            return false;
        }

        $title = 'New Trip Request';
        $message = "Trip from {$trip->pickup_address} to {$trip->destination_address}";

        $data = [
            'type' => 'trip_request',
            'trip_id' => (string) $trip->id,
            'pickup_address' => $trip->pickup_address,
            'destination_address' => $trip->destination_address,
            'estimated_fare' => (string) $trip->estimated_fare,
            'distance' => (string) $trip->estimated_distance . 'km',
            'timeout' => '30'
        ];

        $messageId = $this->firebaseService->sendNotification(
            $driver->fcm_token,
            $title,
            $message,
            $data
        );

        // Store notification record
        TripNotification::create([
            'trip_id' => $trip->id,
            'driver_id' => $driver->id,
            'notification_type' => 'trip_request',
            'message' => $message,
            'sent_at' => now(),
            'fcm_message_id' => $messageId,
        ]);

        return $messageId !== null;
    }

    /**
     * Send driver assigned notification to user
     */
    public function sendDriverAssignedNotification(Trip $trip): bool
    {
        if (!$trip->user->fcm_token) {
            Log::warning('User has no FCM token', ['user_id' => $trip->user_id]);
            return false;
        }

        $driver = $trip->driver;
        $title = 'Driver Assigned';
        $message = "Your driver {$driver->first_name} is on the way!";

        $data = [
            'type' => 'driver_assigned',
            'trip_id' => (string) $trip->id,
            'driver_name' => $driver->first_name,
            'eta' => '5 mins',
            'vehicle' => $driver->vehicle_type . ' ' . ($driver->license_plate ?? '')
        ];

        $messageId = $this->firebaseService->sendNotification(
            $trip->user->fcm_token,
            $title,
            $message,
            $data
        );

        // Store notification record
        TripNotification::create([
            'trip_id' => $trip->id,
            'user_id' => $trip->user_id,
            'notification_type' => 'trip_accepted',
            'message' => $message,
            'sent_at' => now(),
            'fcm_message_id' => $messageId,
        ]);

        return $messageId !== null;
    }

    /**
     * Send driver arriving notification to user
     */
    public function sendDriverArrivingNotification(Trip $trip): bool
    {
        if (!$trip->user->fcm_token) {
            return false;
        }

        $title = 'Driver Arriving';
        $message = 'Your driver is arriving at the pickup location';

        $data = [
            'type' => 'driver_arriving',
            'trip_id' => (string) $trip->id,
            'eta' => '2 mins'
        ];

        $messageId = $this->firebaseService->sendNotification(
            $trip->user->fcm_token,
            $title,
            $message,
            $data
        );

        TripNotification::create([
            'trip_id' => $trip->id,
            'user_id' => $trip->user_id,
            'notification_type' => 'driver_arriving',
            'message' => $message,
            'sent_at' => now(),
            'fcm_message_id' => $messageId,
        ]);

        return $messageId !== null;
    }

    /**
     * Send driver arrived notification to user
     */
    public function sendDriverArrivedNotification(Trip $trip): bool
    {
        if (!$trip->user->fcm_token) {
            return false;
        }

        $title = 'Driver Arrived';
        $message = 'Your driver has arrived at the pickup location';

        $data = [
            'type' => 'driver_arrived',
            'trip_id' => (string) $trip->id,
            'message' => $message
        ];

        $messageId = $this->firebaseService->sendNotification(
            $trip->user->fcm_token,
            $title,
            $message,
            $data
        );

        TripNotification::create([
            'trip_id' => $trip->id,
            'user_id' => $trip->user_id,
            'notification_type' => 'driver_arrived',
            'message' => $message,
            'sent_at' => now(),
            'fcm_message_id' => $messageId,
        ]);

        return $messageId !== null;
    }
}
