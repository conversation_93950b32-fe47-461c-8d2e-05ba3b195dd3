import { createClient, cacheExchange, fetchExchange } from 'urql';
import AsyncStorage from '@react-native-async-storage/async-storage';

const API_URL = 'http://10.0.2.2:8000/graphql';

// Create the GraphQL client
export const graphqlClient = createClient({
  url: API_URL,
  exchanges: [cacheExchange, fetchExchange],
  fetchOptions: async () => {
    const token = await AsyncStorage.getItem('driver_auth_token');
    return {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    };
  },
});

// GraphQL mutations and queries
export const SIGNUP_MUTATION = `
  mutation DriverSignup($input: DriverSignupInput!) {
    driverSignup(input: $input) {
      success
      message
    }
  }
`;

export const SIGNIN_MUTATION = `
  mutation DriverSignin($input: DriverSigninInput!) {
    driverSignin(input: $input) {
      success
      message
    }
  }
`;

export const VERIFY_OTP_MUTATION = `
  mutation DriverVerifyOtp($input: VerifyOtpInput!) {
    driverVerifyOtp(input: $input) {
      success
      message
      token
      user {
        id
        first_name
        last_name
        mobile
        full_name
      }
    }
  }
`;

export const ME_QUERY = `
  query DriverMe {
    driverMe {
      id
      first_name
      last_name
      mobile
      full_name
      mobile_verified_at
      created_at
    }
  }
`;

// Types
export interface Driver {
  id: string;
  first_name: string;
  last_name: string;
  mobile: string;
  full_name: string;
  mobile_verified_at?: string;
  created_at: string;
}

export interface DriverSignupInput {
  first_name: string;
  last_name: string;
  mobile: string;
}

export interface DriverSigninInput {
  mobile: string;
}

export interface VerifyOtpInput {
  mobile: string;
  otp: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
}

export interface AuthTokenResponse extends AuthResponse {
  token?: string;
  user?: Driver;
}
