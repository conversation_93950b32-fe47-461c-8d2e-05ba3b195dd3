{"version": 3, "file": "validateURL.js", "sourceRoot": "", "sources": ["../src/validateURL.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,MAAM,WAAW,CAAC;AAElC,MAAM,UAAU,WAAW,CAAC,GAAW;IACrC,SAAS,CAAC,OAAO,GAAG,KAAK,QAAQ,EAAE,wCAAwC,GAAG,GAAG,CAAC,CAAC;IACnF,SAAS,CAAC,GAAG,EAAE,8BAA8B,CAAC,CAAC;AACjD,CAAC", "sourcesContent": ["import invariant from 'invariant';\n\nexport function validateURL(url: string): void {\n  invariant(typeof url === 'string', 'Invalid URL: should be a string. Was: ' + url);\n  invariant(url, 'Invalid URL: cannot be empty');\n}\n"]}