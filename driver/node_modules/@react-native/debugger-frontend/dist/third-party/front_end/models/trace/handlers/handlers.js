import*as e from"../helpers/helpers.js";import*as t from"../types/types.js";import*as n from"../../../core/platform/platform.js";import*as r from"../../cpu_profile/cpu_profile.js";import*as i from"../../../core/root/root.js";const a=[],s=[];let o=1;var c=Object.freeze({__proto__:null,reset:function(){a.length=0,s.length=0},handleEvent:function(e){t.TraceEvents.isTraceEventAnimation(e)&&a.push(e)},finalize:async function(){const t=e.Trace.createMatchedSortedSyntheticEvents(a);s.push(...t),o=3},data:function(){if(3!==o)throw new Error("Animation handler is not finalized");return{animations:s}}});const d=new Map,l=new Map,m=new Map,u=new Map,f=new Map;function g(e){switch(e){case"seller":return"seller";case"bidder":return"bidder";default:return"unknown"}}function T(t){return e.SyntheticEvents.SyntheticEventsManager.registerSyntheticBasedEvent({rawSourceEvent:t,name:"SyntheticAuctionWorkletEvent",s:"t",cat:t.cat,tid:t.tid,ts:t.ts,ph:"I",pid:t.args.data.pid,host:t.args.data.host,target:t.args.data.target,type:g(t.args.data.type)})}function h(){return{worklets:new Map(m)}}var v=Object.freeze({__proto__:null,reset:function(){d.clear(),l.clear(),m.clear(),u.clear(),f.clear()},handleEvent:function(e){if(t.TraceEvents.isTraceEventAuctionWorkletRunningInProcess(e))d.set(e.args.data.pid,e);else if(t.TraceEvents.isTraceEventAuctionWorkletDoneWithProcess(e))l.set(e.args.data.pid,e);else if(t.TraceEvents.isThreadName(e)){if("auction_worklet.CrUtilityMain"===e.args.name)return void u.set(e.pid,e);"AuctionV8HelperThread"===e.args.name&&f.set(e.pid,e)}},finalize:async function(){for(const[e,t]of u){const n=f.get(e);if(!n)continue;const r=d.get(e),i=l.get(e);let a=null;r?(a={...T(r),args:{data:{runningInProcessEvent:r,utilityThread:t,v8HelperThread:n}}},i&&(a.args.data.doneWithProcessEvent=i)):i&&(a={...T(i),args:{data:{doneWithProcessEvent:i,utilityThread:t,v8HelperThread:n}}},r&&(a.args.data.runningInProcessEvent=r)),null!==a&&m.set(e,a)}},data:h});let p=1;const E=[],w=new Map,M=new Map,I=new Map,S=[],y=[],F=[];function P(e,t){return t+"@"+e}var _=Object.freeze({__proto__:null,EnhancedTracesVersion:1,initialize:function(){if(1!==p)throw new Error("Enhanced Traces Handler was not reset");p=2},reset:function(){E.length=0,w.clear(),M.clear(),I.clear(),S.length=0,y.length=0,F.length=0,p=1},handleEvent:function(e){if(2!==p)throw new Error("Enhanced Traces Handler is not initialized while handling event");if(t.TraceEvents.isTraceEventTargetRundown(e)){const t=e.args?.data;w.set(P(t.isolate,t.scriptId),t.v8context),S.find((e=>e.id===t.frame))||S.push({id:t.frame,type:t.frameType,isolate:t.isolate,pid:e.pid,url:t.url}),y.find((e=>e.v8Context===t.v8context))||y.push({id:-1,origin:t.origin,v8Context:t.v8context,auxData:{frameId:t.frame,isDefault:t.isDefault,type:t.contextType}})}else if(t.TraceEvents.isTraceEventScriptRundown(e)){E.push(e);const t=e.args.data;F.find((e=>e.scriptId===t.scriptId&&e.isolate===t.isolate))||F.push({scriptId:t.scriptId,isolate:t.isolate,executionContextId:t.executionContextId,startLine:t.startLine,startColumn:t.startColumn,endLine:t.endLine,endColumn:t.endColumn,hash:t.hash,isModule:t.isModule,url:t.url,hasSourceUrl:t.hasSourceUrl,sourceMapUrl:t.sourceMapUrl})}else if(t.TraceEvents.isTraceEventScriptRundownSource(e)){const t=e.args.data,n=P(t.isolate,t.scriptId);t.sourceText&&M.set(n,t.sourceText),t.length&&I.set(n,t.length)}},finalize:async function(){if(2!==p)throw new Error("Enhanced Traces Handler is not initialized while being finalized");const e=new Map;E.forEach((t=>{const n=t.args.data,r=w.get(P(n.isolate,n.scriptId));r&&e.set(r,n.executionContextId)})),y.forEach((t=>{if(t.v8Context){const n=e.get(t.v8Context);n&&(t.id=n)}})),F.forEach((e=>{const t=P(e.isolate,e.scriptId);e.sourceText=M.get(t),e.length=I.get(t)})),p=3},data:function(){if(3!==p)throw new Error("Enhanced Traces Handler is not finalized");return{targets:S,executionContexts:y,scripts:F}}});let C=[];const R=[],k=[],L=[],D=[];let b=1;const N=["workerStart","redirectStart","redirectEnd","fetchStart","domainLookupStart","domainLookupEnd","connectStart","connectEnd","secureConnectionStart","requestStart","responseStart","responseEnd"],z=["navigationStart","unloadEventStart","unloadEventEnd","redirectStart","redirectEnd","fetchStart","commitNavigationEnd","domainLookupStart","domainLookupEnd","connectStart","connectEnd","secureConnectionStart","requestStart","responseStart","responseEnd","domLoading","domInteractive","domContentLoadedEventStart","domContentLoadedEventEnd","domComplete","loadEventStart","loadEventEnd"];function W(){if(3!==b)throw new Error("UserTimings handler is not finalized");return{performanceMeasures:C.filter((e=>"blink.user_timing"===e.cat)),consoleTimings:C.filter((e=>"blink.console"===e.cat)),performanceMarks:[...k],timestampEvents:[...D]}}var x=Object.freeze({__proto__:null,reset:function(){C.length=0,R.length=0,k.length=0,L.length=0,D.length=0,b=2},handleEvent:function(e){if(2!==b)throw new Error("UserTimings handler is not initialized");[...N,...z].includes(e.name)||(t.TraceEvents.isTraceEventPerformanceMeasure(e)?R.push(e):(t.TraceEvents.isTraceEventPerformanceMark(e)&&k.push(e),t.TraceEvents.isTraceEventConsoleTime(e)&&L.push(e),t.TraceEvents.isTraceEventTimeStamp(e)&&D.push(e)))},finalize:async function(){if(2!==b)throw new Error("UserTimings handler is not initialized");const t=[...R,...L];C=e.Trace.createMatchedSortedSyntheticEvents(t),b=3},data:W});const O=[],B=[],U=[];let q=1;function H(e){for(const n of e){const e=A(n);if(!e)continue;const r={name:n.name,ph:"X",pid:t.TraceEvents.ProcessID(0),tid:t.TraceEvents.ThreadID(0),ts:n.ts,selfTime:t.Timing.MicroSeconds(0),dur:n.dur,cat:"devtools.extension",args:e};t.Extensions.isExtensionPayloadMarker(e)?U.push(r):t.Extensions.isExtensionPayloadTrackEntry(e)&&O.push(r)}}function A(e){const n=t.TraceEvents.isTraceEventPerformanceMark(e)?e.args.data?.detail:e.args.data.beginEvent.args.detail;if(!n)return null;try{const e=JSON.parse(n);return"devtools"in e&&t.Extensions.isValidExtensionPayload(e.devtools)?e.devtools:null}catch(e){return null}}var j=Object.freeze({__proto__:null,handleEvent:function(e){},reset:function(){q=2,O.length=0,B.length=0,U.length=0},finalize:async function(){if(2!==q)throw new Error("ExtensionTraceData handler is not initialized");!function(){const t=W().performanceMeasures,n=W().performanceMarks;H(e.Trace.mergeEventsInOrder(t,n)),e.Extensions.buildTrackDataFromExtensionEntries(O,B)}(),q=3},extractExtensionEntries:H,extensionDataInTiming:A,data:function(){if(3!==q)throw new Error("ExtensionTraceData handler is not finalized");return{extensionTrackData:[...B],extensionMarkers:[...U]}},deps:function(){return["UserTimings"]}});const G=new Map;let V="",$="";const X=new Map;let Y=t.TraceEvents.ProcessID(-1),K=t.TraceEvents.ThreadID(-1),Q=t.TraceEvents.ProcessID(-1),J=t.TraceEvents.ThreadID(-1),Z=null;const ee=new Map,te=new Set,ne={min:t.Timing.MicroSeconds(Number.POSITIVE_INFINITY),max:t.Timing.MicroSeconds(Number.NEGATIVE_INFINITY),range:t.Timing.MicroSeconds(Number.POSITIVE_INFINITY)},re=new Map,ie=new Map,ae=[],se=new Map;let oe=t.Timing.MicroSeconds(-1);const ce=new Set(["B","E","X","I"]);let de=1,le=!0;const me=new Set(["TracingStartedInPage","TracingSessionIdForWorker","TracingStartedInBrowser"]);function ue(e,r){n.MapUtilities.getWithDefault(X,r.processId,(()=>new Map)).set(r.frame,r);const i=n.MapUtilities.getWithDefault(G,r.frame,(()=>new Map)),a=n.MapUtilities.getWithDefault(i,r.processId,(()=>[])),s=a.at(-1);s&&s.frame.url===r.url||a.push({frame:r,window:{min:e.ts,max:t.Timing.MicroSeconds(0),range:t.Timing.MicroSeconds(0)}})}function fe(){if(3!==de)throw new Error("Meta Handler is not finalized");return{traceBounds:{...ne},browserProcessId:Y,browserThreadId:K,processNames:ee,gpuProcessId:Q,gpuThreadId:J===t.TraceEvents.ThreadID(-1)?void 0:J,viewportRect:Z||void 0,mainFrameId:V,mainFrameURL:$,navigationsByFrameId:re,navigationsByNavigationId:ie,threadsInProcess:se,rendererProcessesByFrame:G,topLevelRendererIds:te,frameByProcessId:X,mainFrameNavigations:ae,traceIsGeneric:le}}var ge=Object.freeze({__proto__:null,reset:function(){re.clear(),ie.clear(),ee.clear(),ae.length=0,Y=t.TraceEvents.ProcessID(-1),K=t.TraceEvents.ThreadID(-1),Q=t.TraceEvents.ProcessID(-1),J=t.TraceEvents.ThreadID(-1),Z=null,te.clear(),se.clear(),G.clear(),X.clear(),ne.min=t.Timing.MicroSeconds(Number.POSITIVE_INFINITY),ne.max=t.Timing.MicroSeconds(Number.NEGATIVE_INFINITY),ne.range=t.Timing.MicroSeconds(Number.POSITIVE_INFINITY),oe=t.Timing.MicroSeconds(-1),le=!0,de=1},initialize:function(){if(1!==de)throw new Error("Meta Handler was not reset");de=2},handleEvent:function(e){if(2!==de)throw new Error("Meta Handler is not initialized");if(le&&me.has(e.name)&&(le=!1),t.TraceEvents.isProcessName(e)&&ee.set(e.pid,e),0!==e.ts&&!e.name.endsWith("::UMA")&&ce.has(e.ph)){ne.min=t.Timing.MicroSeconds(Math.min(e.ts,ne.min));const n=e.dur||t.Timing.MicroSeconds(0);ne.max=t.Timing.MicroSeconds(Math.max(e.ts+n,ne.max))}if(!t.TraceEvents.isProcessName(e)||"Browser"!==e.args.name&&"HeadlessBrowser"!==e.args.name)if(!t.TraceEvents.isProcessName(e)||"Gpu"!==e.args.name&&"GPU Process"!==e.args.name)if(t.TraceEvents.isThreadName(e)&&"CrGpuMain"===e.args.name)J=e.tid;else{if(t.TraceEvents.isThreadName(e)&&"CrBrowserMain"===e.args.name&&(K=e.tid),t.TraceEvents.isTraceEventMainFrameViewport(e)&&null===Z){const t=e.args.data.viewport_rect,n=t[0],r=t[1],i=t[2],a=t[5];Z=new DOMRect(n,r,i,a)}if(t.TraceEvents.isTraceEventTracingStartedInBrowser(e)){if(oe=e.ts,!e.args.data)throw new Error("No frames found in trace data");for(const t of e.args.data.frames??[]){ue(e,t),t.parent||te.add(t.processId);const n="isOutermostMainFrame"in t;"isInPrimaryMainFrame"in t&&n?t.isInPrimaryMainFrame&&t.isOutermostMainFrame&&(V=t.frame,$=t.url):n?t.isOutermostMainFrame&&(V=t.frame,$=t.url):!t.parent&&t.url&&(V=t.frame,$=t.url)}}else if(t.TraceEvents.isTraceEventFrameCommittedInBrowser(e)){const t=e.args.data;if(!t)return;if(ue(e,t),t.parent)return;te.add(t.processId)}else if(t.TraceEvents.isTraceEventCommitLoad(e)){const t=e.args.data;if(!t)return;const{frame:n,name:r,url:i}=t;ue(e,{processId:e.pid,frame:n,name:r,url:i})}else if(t.TraceEvents.isThreadName(e)){n.MapUtilities.getWithDefault(se,e.pid,(()=>new Map)).set(e.tid,e)}else if(t.TraceEvents.isTraceEventNavigationStartWithURL(e)&&e.args.data){const t=e.args.data.navigationId;if(ie.has(t))return;ie.set(t,e);const n=e.args.frame,r=re.get(n)||[];return r.push(e),re.set(n,r),void(n===V&&ae.push(e))}}else Q=e.pid;else Y=e.pid},finalize:async function(){if(2!==de)throw new Error("Handler is not initialized");oe>=0&&(ne.min=oe),ne.range=t.Timing.MicroSeconds(ne.max-ne.min);for(const[,e]of G){const n=[...e.values()].flat();for(let e=0;e<n.length;e++){const r=n[e],i=n[e+1];i?(r.window.max=t.Timing.MicroSeconds(i.window.min-1),r.window.range=t.Timing.MicroSeconds(r.window.max-r.window.min)):(r.window.max=t.Timing.MicroSeconds(ne.max),r.window.range=t.Timing.MicroSeconds(ne.max-r.window.min))}}for(const[e,t]of re)if(!G.has(e)){re.delete(e);for(const e of t)e.args.data&&ie.delete(e.args.data.navigationId)}const n=ae.at(0),r=e.Timing.secondsToMicroseconds(t.Timing.Seconds(.5));if(n){const e=n.ts-ne.min<r;n.args.data?.isOutermostMainFrame&&n.args.data?.documentLoaderURL&&e&&($=n.args.data.documentLoaderURL)}de=3},data:fe});let Te=1;const he=[],ve=[],pe=new Map;let Ee={},we=null;const Me=[],Ie=[];function Se(){return{paints:he,snapshots:ve,paintsToSnapshots:pe}}var ye=Object.freeze({__proto__:null,reset:function(){Te=1,he.length=0,ve.length=0,pe.clear(),Ee={},we=null,Me.length=0,Ie.length=0},initialize:function(){if(1!==Te)throw new Error("LayerTree Handler was not reset before being initialized");Te=2},handleEvent:function(e){(t.TraceEvents.isTraceEventPaint(e)||t.TraceEvents.isTraceEventDisplayListItemListSnapshot(e)||t.TraceEvents.isTraceEventUpdateLayer(e)||t.TraceEvents.isTraceEventSetLayerId(e))&&Ie.push(e)},finalize:async function(){if(2!==Te)throw new Error("LayerTree Handler is not initialized");const n=fe();e.Trace.sortTraceEventsInPlace(Ie);for(const e of Ie)if(t.TraceEvents.isTraceEventSetLayerId(e)){if(n.mainFrameId!==e.args.data.frame)continue;we=e.args.data.layerTreeId}else if(t.TraceEvents.isTraceEventUpdateLayer(e))Me.push(e);else{if(t.TraceEvents.isTraceEventPaint(e)){if(!e.args.data.layerId)continue;he.push(e),Ee[e.args.data.layerId]=e;continue}if(t.TraceEvents.isTraceEventDisplayListItemListSnapshot(e)){let t=null;for(let n=Me.length-1;n>-1;n--){const r=Me[n];if(r.pid===e.pid&&r.tid===e.tid){t=r;break}}if(!t)continue;if(t.args.layerTreeId!==we)continue;const n=Ee[t.args.layerId];if(!n)continue;ve.push(e),pe.set(n,e)}}Te=3},data:Se,deps:function(){return["Meta"]}});const Fe=new Map,Pe=new Map,_e=new Map,Ce=new Map;let Re=1;function ke(){if(3!==Re)throw new Error("Samples Handler is not finalized");return{profilesInProcess:Pe,entryToNode:_e}}function Le(e,t){const r=n.MapUtilities.getWithDefault(Ce,e,(()=>new Map));return n.MapUtilities.getWithDefault(r,t,(()=>({rawProfile:{startTime:0,endTime:0,nodes:[],samples:[],timeDeltas:[],lines:[]},profileId:t})))}var De=Object.freeze({__proto__:null,reset:function(){Fe.clear(),Ce.clear(),Pe.clear(),_e.clear(),Re=1},initialize:function(){if(1!==Re)throw new Error("Samples Handler was not reset");Re=2},handleEvent:function(e){if(2!==Re)throw new Error("Samples Handler is not initialized");if(t.TraceEvents.isSyntheticCpuProfile(e)){const t=e.pid,n=e.tid,r=Le(t,"0x1");return r.rawProfile=e.args.data.cpuProfile,void(r.threadId=n)}if(t.TraceEvents.isTraceEventProfile(e)){const t=Le(e.pid,e.id);return t.rawProfile.startTime=e.ts,void(t.threadId=e.tid)}if(t.TraceEvents.isTraceEventProfileChunk(e)){const t=Le(e.pid,e.id).rawProfile,n=e.args?.data?.cpuProfile||{samples:[]},r=n?.samples||[],i=[];for(const e of n?.nodes||[]){const t=void 0===e.callFrame.lineNumber?-1:e.callFrame.lineNumber,n=void 0===e.callFrame.columnNumber?-1:e.callFrame.columnNumber,r=String(e.callFrame.scriptId),a=e.callFrame.url||"",s={...e,callFrame:{...e.callFrame,url:a,lineNumber:t,columnNumber:n,scriptId:r}};i.push(s)}const a=e.args.data?.timeDeltas||[],s=e.args.data?.lines||Array(r.length).fill(0);if(t.nodes.push(...i),t.samples?.push(...r),t.timeDeltas?.push(...a),t.lines?.push(...s),t.samples&&t.timeDeltas&&t.samples.length!==t.timeDeltas.length)return void console.error("Failed to parse CPU profile.");if(!t.endTime&&t.timeDeltas){const e=t.timeDeltas;t.endTime=e.reduce(((e,t)=>e+t),t.startTime)}}else;},finalize:async function(){if(2!==Re)throw new Error("Samples Handler is not initialized");!function(){for(const[s,o]of Ce)for(const[c,d]of o){const l=d.threadId;if(!d.rawProfile.nodes.length||void 0===l)continue;const m=[],u=new r.CPUProfileDataModel.CPUProfileDataModel(d.rawProfile),f=e.TreeHelpers.makeEmptyTraceEntryTree();f.maxDepth=u.maxDepth;const g={rawProfile:d.rawProfile,parsedProfile:u,profileCalls:[],profileTree:f,profileId:c},T=n.MapUtilities.getWithDefault(Pe,s,(()=>new Map));function i(n,r,i,a){if(void 0===l)return;const o=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(a)),d=r.id,u=e.Trace.makeProfileCall(r,c,i,o,s,l);g.profileCalls.push(u),m.push(g.profileCalls.length-1);const f=e.TreeHelpers.makeEmptyTraceEntryNode(u,d);_e.set(u,f),f.depth=n,1===m.length&&g.profileTree?.roots.add(f)}function a(n,r,i,a,s,o){const d=m.pop(),l=void 0!==d&&g.profileCalls[d];if(!l)return;const{callFrame:u,ts:f,pid:T,tid:h}=l,v=_e.get(l);if(void 0===u||void 0===f||void 0===T||void 0===c||void 0===h||void 0===v)return;const p=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(s)),E=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(o));l.dur=p,l.selfTime=E;const w=m.at(-1),M=void 0!==w&&g.profileCalls.at(w),I=M&&_e.get(M);I&&(v.parent=I,I.children.push(v))}u.forEachFrame(i,a),T.set(l,g)}}(),Re=3},data:ke,getProfileCallFunctionName:function(e,t){const n=e.profilesInProcess.get(t.pid)?.get(t.tid),r=n?.parsedProfile.nodeById(t.nodeId);return r?.functionName?r.functionName:t.callFrame.functionName}});let be;const Ne=new Map,ze=Array(),We=new Map;let xe=[];const Oe=[];let Be=1,Ue=t.Configuration.defaults();const qe=()=>({url:null,isOnMainFrame:!1,threads:new Map}),He=()=>({name:null,entries:[],profileCalls:[]}),Ae=(e,t)=>n.MapUtilities.getWithDefault(e,t,qe),je=(e,t)=>n.MapUtilities.getWithDefault(e.threads,t,He);function Ge(){if(3!==Be)throw new Error("Renderer Handler is not finalized");return{processes:new Map(Ne),compositorTileWorkers:new Map(Ve()),entryToNode:new Map(We),allTraceEntries:[...xe]}}function Ve(){const e=new Map;for(const t of ze){const n=e.get(t.pid)||[];n.push(t.tid),e.set(t.pid,n)}return e}function $e(e,t,n,r){Xe(e,n),Ye(e,t,n),Ke(e,n,r)}function Xe(e,t){for(const n of t.values())for(const[t,r]of n)for(const n of r.flat()){const r=Ae(e,t);if(null===r.url||"about:blank"===r.url)try{new URL(n.frame.url),r.url=n.frame.url}catch(e){r.url=null}}}function Ye(e,t,n){for(const[r,i]of n)for(const[n]of i){const i=Ae(e,n);r===t&&(i.isOnMainFrame=!0)}}function Ke(e,t,n){for(const[t,r]of e)for(const[e,i]of n.get(t)??[]){je(r,e).name=i?.args.name??`${e}`}}function Qe(e){if(be)return;const t=h().worklets;if(!fe().traceIsGeneric)for(const[n,r]of e)if(null!==r.url);else{const i=t.get(n);i?r.url=i.host:e.delete(n)}}function Je(e){for(const[,t]of e)for(const[e,n]of t.threads)n.tree?.roots.size||t.threads.delete(e)}function Ze(t,n){const r=ke();for(const[i,a]of t)for(const[t,s]of a.threads){if(!s.entries.length){s.tree=e.TreeHelpers.makeEmptyTraceEntryTree();continue}e.Trace.sortTraceEventsInPlace(s.entries);const a=r.profilesInProcess.get(i)?.get(t);if(a){const n=a.parsedProfile,r=n&&new e.SamplesIntegrator.SamplesIntegrator(n,a.profileId,i,t,Ue),o=r?.buildProfileCalls(s.entries);if(r&&o){xe=[...xe,...o],s.entries=e.Trace.mergeEventsInOrder(s.entries,o),s.profileCalls=o;const t=r.jsSampleEvents;t&&(xe=[...xe,...t],s.entries=e.Trace.mergeEventsInOrder(s.entries,t))}}const o=e.TreeHelpers.treify(s.entries,n);s.tree=o.tree;for(const[e,t]of o.entryToNode)We.set(e,t)}}function et(e){if(t.TraceEvents.isTraceEventEnd(e)){const n=Oe.pop();return n?n.name!==e.name||n.cat!==e.cat?(console.error("Begin/End events mismatch at "+n.ts+" ("+n.name+") vs. "+e.ts+" ("+e.name+")"),null):(n.dur=t.Timing.MicroSeconds(e.ts-n.ts),null):null}const n={...e,ph:"X",dur:t.Timing.MicroSeconds(0)};return Oe.push(n),n}var tt=Object.freeze({__proto__:null,handleUserConfig:function(e){Ue=e},reset:function(){Ne.clear(),We.clear(),xe.length=0,Oe.length=0,ze.length=0,Be=1},initialize:function(){if(1!==Be)throw new Error("Renderer Handler was not reset");be=i.Runtime.experiments.isEnabled("react-native-specific-ui"),Be=2},handleEvent:function(e){if(2!==Be)throw new Error("Renderer Handler is not initialized");if(t.TraceEvents.isThreadName(e)&&e.args.name?.startsWith("CompositorTileWorker")&&ze.push({pid:e.pid,tid:e.tid}),t.TraceEvents.isTraceEventBegin(e)||t.TraceEvents.isTraceEventEnd(e)){const t=Ae(Ne,e.pid),n=je(t,e.tid),r=et(e);if(!r)return;return n.entries.push(r),void xe.push(r)}if(t.TraceEvents.isTraceEventInstant(e)||t.TraceEvents.isTraceEventComplete(e)){const t=Ae(Ne,e.pid);je(t,e.tid).entries.push(e),xe.push(e)}},finalize:async function(){if(2!==Be)throw new Error("Renderer Handler is not initialized");const{mainFrameId:t,rendererProcessesByFrame:n,threadsInProcess:r}=fe();$e(Ne,t,n,r),Qe(Ne),Ze(Ne),Je(Ne),e.Trace.sortTraceEventsInPlace(xe),Be=3},data:Ge,assignMeta:$e,assignOrigin:Xe,assignIsMainFrame:Ye,assignThreadName:Ke,sanitizeProcesses:Qe,sanitizeThreads:Je,buildHierarchy:Ze,makeCompleteEvent:et,deps:function(){return["Meta","Samples","AuctionWorklets"]}});function nt(e,t,n){let r="OTHER";return"CrRendererMain"===t.name?r="MAIN_THREAD":"DedicatedWorker thread"===t.name?r="WORKER":t.name?.startsWith("CompositorTileWorker")?r="RASTERIZER":n.worklets.has(e)?r="AUCTION_WORKLET":t.name?.startsWith("ThreadPool")&&(r="THREAD_POOL"),r}function rt(e,t){const n=[];if(e.processes.size)for(const[r,i]of e.processes)for(const[a,s]of i.threads){if(!s.tree)continue;const o=nt(r,s,t);n.push({name:s.name,pid:r,tid:a,processIsOnMainFrame:i.isOnMainFrame,entries:s.entries,tree:s.tree,type:o,entryToNode:e.entryToNode})}return n}var it=Object.freeze({__proto__:null,threadsInRenderer:rt,threadsInTrace:function(e){const t=rt(e.Renderer,e.AuctionWorklets);if(t.length)return t;const n=[];if(e.Samples.profilesInProcess.size)for(const[t,r]of e.Samples.profilesInProcess)for(const[i,a]of r)a.profileTree&&n.push({pid:t,tid:i,name:null,entries:a.profileCalls,processIsOnMainFrame:!1,tree:a.profileTree,type:"CPU_PROFILE",entryToNode:e.Samples.entryToNode});return n}});let at=1;const st=[];let ot=null;class ct{#e=[];#t={};#n=new gt;#r=null;#i=!1;#a=!1;#s=null;#o=null;#c=null;#d=null;#l=null;#m=null;#u=null;#f=null;#g=null;#T;constructor(e,t,n,r,i){const a=rt(t,n).filter((e=>"MAIN_THREAD"===e.type&&e.processIsOnMainFrame)).map((e=>({tid:e.tid,pid:e.pid,startTime:e.entries[0].ts})));this.#T=i,this.#h(e,a,r.mainFrameId)}framesById(){return this.#t}frames(){return this.#e}#v(e,t){this.#r||this.#p(e,t),this.#d=e,this.#n.addFrameIfNotExists(t,e,!1,!1)}#E(e,t,n){this.#r||this.#p(e,t),this.#n.addFrameIfNotExists(t,e,!0,n),this.#n.setDropped(t,!0),this.#n.setPartial(t,n)}#w(e,t){if(this.#r){if(this.#i||!this.#a){if(this.#l){(this.#o?this.#o.triggerTime:this.#d||this.#l)>this.#r.startTime&&(this.#r.idle=!0,this.#d=null),this.#l=null}const e=this.#n.processPendingBeginFramesOnDrawFrame(t);for(const n of e){const e=this.#r.idle;this.#p(n.startTime,t),e&&this.#o&&this.#M(),n.isDropped&&(this.#r.dropped=!0),n.isPartial&&(this.#r.isPartial=!0)}}this.#i=!1}else this.#p(e,t)}#I(){this.#r&&this.#o&&!this.#l&&this.#M()}#S(){this.#r&&(this.#a=!0)}#y(){this.#c&&(this.#o=this.#c,this.#c=null,this.#a=!1,this.#i=!0)}#F(e){this.#s=e}#P(e,t){t&&(this.#l=e)}#p(e,n){this.#r&&this.#_(this.#r,e),this.#r=new lt(n,e,t.Timing.MicroSeconds(e-fe().traceBounds.min))}#_(e,t){e.setLayerTree(this.#s),e.setEndTime(t),this.#s&&(this.#s.paints=e.paints);const n=this.#e[this.#e.length-1];this.#e.length&&n&&(e.startTime!==n.endTime||e.startTime>e.endTime)&&console.assert(!1,`Inconsistent frame time for frame ${this.#e.length} (${e.startTime} - ${e.endTime})`),this.#e.push(e),"number"==typeof e.mainFrameId&&(this.#t[e.mainFrameId]=e)}#M(){this.#o&&this.#r&&(this.#r.paints=this.#o.paints,this.#r.mainFrameId=this.#o.mainFrameId,this.#o=null)}#h(e,t,n){let r=0;this.#g=t.length&&t[0].tid||null,this.#f=t.length&&t[0].pid||null;for(let i=0;i<e.length;++i){for(;r+1<t.length&&t[r+1].startTime<=e[i].ts;)this.#g=t[++r].tid,this.#f=t[r].pid;this.#C(e[i],n)}this.#g=null,this.#f=null}#C(e,n){t.TraceEvents.isTraceEventSetLayerId(e)&&e.args.data.frame===n?this.#u=e.args.data.layerTreeId:t.TraceEvents.isTraceEventLayerTreeHostImplSnapshot(e)&&Number(e.id)===this.#u?this.#F({entry:e,paints:[]}):(function(e){return t.TraceEvents.isTraceEventSetLayerId(e)||t.TraceEvents.isTraceEventBeginFrame(e)||t.TraceEvents.isTraceEventDroppedFrame(e)||t.TraceEvents.isTraceEventRequestMainThreadFrame(e)||t.TraceEvents.isTraceEventBeginMainThreadFrame(e)||t.TraceEvents.isTraceEventNeedsBeginFrameChanged(e)||t.TraceEvents.isTraceEventCommit(e)||t.TraceEvents.isTraceEventCompositeLayers(e)||t.TraceEvents.isTraceEventActivateLayerTree(e)||t.TraceEvents.isTraceEventDrawFrame(e)}(e)&&this.#R(e),e.tid===this.#g&&e.pid===this.#f&&this.#k(e))}#R(e){e.args.layerTreeId===this.#u&&(t.TraceEvents.isTraceEventBeginFrame(e)?this.#v(e.ts,e.args.frameSeqId):t.TraceEvents.isTraceEventDrawFrame(e)?this.#w(e.ts,e.args.frameSeqId):t.TraceEvents.isTraceEventActivateLayerTree(e)?this.#I():t.TraceEvents.isTraceEventRequestMainThreadFrame(e)?this.#S():t.TraceEvents.isTraceEventNeedsBeginFrameChanged(e)?this.#P(e.ts,e.args.data&&Boolean(e.args.data.needsBeginFrame)):t.TraceEvents.isTraceEventDroppedFrame(e)&&this.#E(e.ts,e.args.frameSeqId,Boolean(e.args.hasPartialUpdate)))}#k(e){if(function(e){return"RunTask"===e.name&&e.cat.includes("disabled-by-default-devtools.timeline")}(e)&&(this.#m=e.ts),!this.#c&&dt.has(e.name)&&(this.#c=new ut(this.#m||e.ts)),this.#c){if(t.TraceEvents.isTraceEventBeginMainThreadFrame(e)&&e.args.data.frameId&&(this.#c.mainFrameId=e.args.data.frameId),t.TraceEvents.isTraceEventPaint(e)){const t=this.#T.paintsToSnapshots.get(e);t&&this.#c.paints.push(new mt(e,t))}(t.TraceEvents.isTraceEventCompositeLayers(e)||t.TraceEvents.isTraceEventCommit(e))&&e.args.layerTreeId===this.#u&&this.#y()}}}const dt=new Set(["ScheduleStyleRecalculation","InvalidateLayout","BeginMainThreadFrame","ScrollLayer"]);class lt{startTime;startTimeOffset;endTime;duration;idle;dropped;isPartial;layerTree;paints;mainFrameId;seqId;constructor(e,n,r){this.seqId=e,this.startTime=n,this.startTimeOffset=r,this.endTime=this.startTime,this.duration=t.Timing.MicroSeconds(0),this.idle=!1,this.dropped=!1,this.isPartial=!1,this.layerTree=null,this.paints=[],this.mainFrameId=void 0}setEndTime(e){this.endTime=e,this.duration=t.Timing.MicroSeconds(this.endTime-this.startTime)}setLayerTree(e){this.layerTree=e}}class mt{#L;#D;constructor(e,t){this.#L=e,this.#D=t}layerId(){return this.#L.args.data.layerId}event(){return this.#L}picture(){const e=this.#D.args.snapshot.params?.layer_rect,t=this.#D.args.snapshot.skp64;return e&&t?{rect:e,serializedPicture:t}:null}}class ut{paints;mainFrameId;triggerTime;constructor(e){this.paints=[],this.mainFrameId=void 0,this.triggerTime=e}}class ft{seqId;startTime;isDropped;isPartial;constructor(e,t,n,r){this.seqId=e,this.startTime=t,this.isDropped=n,this.isPartial=r}}class gt{queueFrames=[];mapFrames={};addFrameIfNotExists(e,t,n,r){e in this.mapFrames||(this.mapFrames[e]=new ft(e,t,n,r),this.queueFrames.push(e))}setDropped(e,t){e in this.mapFrames&&(this.mapFrames[e].isDropped=t)}setPartial(e,t){e in this.mapFrames&&(this.mapFrames[e].isPartial=t)}processPendingBeginFramesOnDrawFrame(e){const t=[];if(e in this.mapFrames){for(;this.queueFrames[0]!==e;){const e=this.queueFrames[0];this.mapFrames[e].isDropped&&t.push(this.mapFrames[e]),delete this.mapFrames[e],this.queueFrames.shift()}t.push(this.mapFrames[e]),delete this.mapFrames[e],this.queueFrames.shift()}return t}}var Tt=Object.freeze({__proto__:null,reset:function(){at=1,st.length=0},initialize:function(){if(1!==at)throw new Error("FramesHandler was not reset before being initialized");at=2},handleEvent:function(e){st.push(e)},finalize:async function(){if(2!==at)throw new Error("FramesHandler is not initialized");e.Trace.sortTraceEventsInPlace(st);const t=new ct(st,Ge(),h(),fe(),Se());ot=t},data:function(){return{frames:ot?Array.from(ot.frames()):[],framesById:ot?{...ot.framesById()}:{}}},deps:function(){return["Meta","Renderer","AuctionWorklets","LayerTree"]},TimelineFrameModel:ct,TimelineFrame:lt,LayerPaintEvent:mt,PendingFrame:ut,TimelineFrameBeginFrameQueue:gt,framesWithinWindow:function(e,t,r){const i=n.ArrayUtilities.lowerBound(e,t||0,((e,t)=>e-t.endTime)),a=n.ArrayUtilities.lowerBound(e,r||1/0,((e,t)=>e-t.startTime));return e.slice(i,a)}});let ht=1;const vt=new Map;let pt=[];var Et=Object.freeze({__proto__:null,reset:function(){vt.clear(),pt=[],ht=1},initialize:function(){if(1!==ht)throw new Error("GPU Handler was not reset before being initialized");ht=2},handleEvent:function(n){if(2!==ht)throw new Error("GPU Handler is not initialized");t.TraceEvents.isTraceEventGPUTask(n)&&e.Trace.addEventToProcessThread(n,vt)},finalize:async function(){if(2!==ht)throw new Error("GPU Handler is not initialized");const{gpuProcessId:e,gpuThreadId:t}=fe(),n=vt.get(e);n&&t&&(pt=n.get(t)||[]),ht=3},data:function(){if(3!==ht)throw new Error("GPU Handler is not finalized");return{mainGPUThreadTasks:pt}},deps:function(){return["Meta"]}});const wt=new Map,Mt=new Map,It=new Map,St=new Map;var yt=Object.freeze({__proto__:null,reset:function(){wt.clear(),Mt.clear(),It.clear(),St.clear()},handleEvent:function(e){if(t.TraceEvents.isTraceEventPaintImage(e)){const t=wt.get(e.pid)||new Map,n=t.get(e.tid)||[];return n.push(e),t.set(e.tid,n),void wt.set(e.pid,t)}if(t.TraceEvents.isTraceEventDecodeLazyPixelRef(e)&&void 0!==e.args?.LazyPixelRef){const t=Mt.get(e.pid)||new Map,n=t.get(e.tid)||[];n.push(e),t.set(e.tid,n),Mt.set(e.pid,t)}if(t.TraceEvents.isTraceEventDrawLazyPixelRef(e)&&void 0!==e.args?.LazyPixelRef){const t=wt.get(e.pid)?.get(e.tid)?.at(-1);if(!t)return;It.set(e.args.LazyPixelRef,t)}else if(t.TraceEvents.isTraceEventDecodeImage(e)){const t=wt.get(e.pid)?.get(e.tid)?.at(-1);if(t)return void St.set(e,t);const n=Mt.get(e.pid)?.get(e.tid)?.at(-1);if(!n||void 0===n.args?.LazyPixelRef)return;const r=It.get(n.args.LazyPixelRef);if(!r)return;St.set(e,r)}},data:function(){return{paintImageByDrawLazyPixelRef:It,paintImageForEvent:St}}});let Ft=1;const Pt=new Map,_t=new Map,Ct=new Map,Rt=[],kt=new Map,Lt=new Map,Dt=new Map,bt=new Map,Nt=new Map,zt=new Map,Wt=new Map;function xt(e){Lt.set(e.event,e.initiator);const t=Dt.get(e.initiator)||[];t.push(e.event),Dt.set(e.initiator,t)}var Ot=Object.freeze({__proto__:null,reset:function(){Pt.clear(),_t.clear(),Ct.clear(),Nt.clear(),Lt.clear(),Dt.clear(),bt.clear(),zt.clear(),Wt.clear(),kt.clear(),Rt.length=0,Ft=1},initialize:function(){if(1!==Ft)throw new Error("InitiatorsHandler was not reset before being initialized");Ft=2},handleEvent:function(n){if(t.TraceEvents.isTraceEventScheduleStyleRecalculation(n))Pt.set(n.args.data.frame,n);else if(t.TraceEvents.isTraceEventUpdateLayoutTree(n)){if(n.args.beginData){Ct.set(n.args.beginData.frame,n);const e=Pt.get(n.args.beginData.frame);e&&xt({event:n,initiator:e})}}else if(t.TraceEvents.isTraceEventInvalidateLayout(n)){let t=n;if(!_t.has(n.args.data.frame)){const r=Ct.get(n.args.data.frame);if(r){const{endTime:i}=e.Timing.eventTimingsMicroSeconds(r),a=Lt.get(r);a&&i&&i>n.ts&&(t=a)}}_t.set(n.args.data.frame,t)}else if(t.TraceEvents.isTraceEventLayout(n)){const e=_t.get(n.args.beginData.frame);e&&xt({event:n,initiator:e}),_t.delete(n.args.beginData.frame)}else if(t.TraceEvents.isTraceEventRequestAnimationFrame(n))bt.set(n.args.data.id,n);else if(t.TraceEvents.isTraceEventFireAnimationFrame(n)){const e=bt.get(n.args.data.id);e&&xt({event:n,initiator:e})}else if(t.TraceEvents.isTraceEventTimerInstall(n))Nt.set(n.args.data.timerId,n);else if(t.TraceEvents.isTraceEventTimerFire(n)){const e=Nt.get(n.args.data.timerId);e&&xt({event:n,initiator:e})}else if(t.TraceEvents.isTraceEventRequestIdleCallback(n))zt.set(n.args.data.id,n);else if(t.TraceEvents.isTraceEventFireIdleCallback(n)){const e=zt.get(n.args.data.id);e&&xt({event:n,initiator:e})}else if(t.TraceEvents.isTraceEventWebSocketCreate(n))Wt.set(n.args.data.identifier,n);else if(t.TraceEvents.isTraceEventWebSocketInfo(n)||t.TraceEvents.isTraceEventWebSocketTransfer(n)){const e=Wt.get(n.args.data.identifier);e&&xt({event:n,initiator:e})}else if(t.TraceEvents.isTraceEventHandlePostMessage(n))Rt.push(n);else if(t.TraceEvents.isTraceEventSchedulePostMessage(n)){const e=n.args.data?.traceId;e&&kt.set(e,n)}},finalize:async function(){if(2!==Ft)throw new Error("InitiatorsHandler is not initialized");!function(){for(const e of Rt){const t=e.args.data?.traceId,n=kt.get(t);n&&xt({event:e,initiator:n})}}(),Ft=3},data:function(){return{eventToInitiator:Lt,initiatorToEvents:Dt}}});let Bt=1;const Ut=new Map;let qt=null,Ht=!1;const At=[];function jt(e,t){const n=Ut.get(e)||[];n.push(t),Ut.set(e,n)}var Gt=Object.freeze({__proto__:null,reset:function(){Bt=1,Ut.clear(),qt=null,At.length=0,Ht=!1},initialize:function(){if(1!==Bt)throw new Error("InvalidationsHandler was not reset before being initialized");Bt=2},handleEvent:function(e){if(t.TraceEvents.isTraceEventUpdateLayoutTree(e)){qt=e;for(const n of At){if(t.TraceEvents.isTraceEventLayoutInvalidationTracking(n))continue;const r=qt.args.beginData?.frame;r&&n.args.data.frame===r&&jt(e,n)}}else if(t.TraceEvents.isTraceEventInvalidationTracking(e)){if(Ht&&(At.length=0,qt=null,Ht=!1),qt&&(t.TraceEvents.isTraceEventScheduleStyleInvalidationTracking(e)||t.TraceEvents.isTraceEventStyleRecalcInvalidationTracking(e)||t.TraceEvents.isTraceEventStyleInvalidatorInvalidationTracking(e))){const t=qt.ts+(qt.dur||0);e.ts>=qt.ts&&e.ts<=t&&qt.args.beginData?.frame===e.args.data.frame&&jt(qt,e)}At.push(e)}else if(t.TraceEvents.isTraceEventPaint(e))Ht=!0;else if(t.TraceEvents.isTraceEventLayout(e)){const n=e.args.beginData.frame;for(const r of At)t.TraceEvents.isTraceEventLayoutInvalidationTracking(r)&&r.args.data.frame===n&&jt(e,r)}},finalize:async function(){if(2!==Bt)throw new Error("InvalidationsHandler is not initialized");Bt=3},data:function(){return{invalidationsForEvent:Ut}}});const Vt=new Map;var $t=Object.freeze({__proto__:null,reset:function(){Vt.clear()},handleEvent:function(e){t.TraceEvents.isTraceEventLargestImagePaintCandidate(e)&&e.args.data&&Vt.set(e.args.data.DOMNodeId,e)},data:function(){return Vt}});const Xt=new Map;var Yt=Object.freeze({__proto__:null,reset:function(){Xt.clear()},handleEvent:function(e){t.TraceEvents.isTraceEventLargestTextPaintCandidate(e)&&e.args.data&&Xt.set(e.args.data.DOMNodeId,e)},data:function(){return Xt}});const Kt=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(5e3)),Qt=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(1e3)),Jt=[],Zt=[],en=[],tn=[],nn=[],rn=new Set,an=[];let sn=0,on=-1;const cn=[],dn=[];let ln=1;function mn(e){return{min:e,max:e,range:t.Timing.MicroSeconds(0)}}function un(e,n){e.max=n,e.range=t.Timing.MicroSeconds(e.max-e.min)}var fn=Object.freeze({__proto__:null,MAX_CLUSTER_DURATION:Kt,MAX_SHIFT_TIME_DELTA:Qt,initialize:function(){if(1!==ln)throw new Error("LayoutShifts Handler was not reset");ln=2},reset:function(){ln=1,Jt.length=0,Zt.length=0,en.length=0,tn.length=0,an.length=0,nn.length=0,rn.clear(),cn.length=0,sn=0,dn.length=0,on=-1},handleEvent:function(e){if(2!==ln)throw new Error("Handler is not initialized");!t.TraceEvents.isTraceEventLayoutShift(e)||e.args.data?.had_recent_input?t.TraceEvents.isTraceEventLayoutInvalidationTracking(e)?Zt.push(e):(t.TraceEvents.isTraceEventScheduleStyleInvalidationTracking(e)&&en.push(e),t.TraceEvents.isTraceEventStyleRecalcInvalidationTracking(e)&&tn.push(e),t.TraceEvents.isTraceEventPrePaint(e)?an.push(e):t.TraceEvents.isTraceEventRenderFrameImplCreateChildFrame(e)&&nn.push(e)):Jt.push(e)},finalize:async function(){Jt.sort(((e,t)=>e.ts-t.ts)),an.sort(((e,t)=>e.ts-t.ts)),Zt.sort(((e,t)=>e.ts-t.ts)),nn.sort(((e,t)=>e.ts-t.ts)),await async function(){const{navigationsByFrameId:r,mainFrameId:i,traceBounds:a}=fe(),s=r.get(i)||[];if(0===Jt.length)return;let o=Jt[0].ts,c=Jt[0].ts,d=null;for(const r of Jt){const i=r.ts-o>Kt,a=r.ts-c>Qt,l=n.ArrayUtilities.nearestIndexFromEnd(s,(e=>e.ts<r.ts)),m=d!==l&&null!==l;if(i||a||m||!cn.length){const e=r.ts,n=i?o+Kt:1/0,d=a?c+Qt:1/0,u=m?s[l].ts:1/0,f=Math.min(n,d,u);if(cn.length>0){un(cn[cn.length-1].clusterWindow,t.Timing.MicroSeconds(f))}const g=null===l?void 0:s[l].args.data?.navigationId;cn.push({events:[],clusterWindow:mn(e),clusterCumulativeScore:0,scoreWindows:{good:mn(e),needsImprovement:null,bad:null},navigationId:g}),o=e}const u=cn[cn.length-1],f=null!==l?t.Timing.MicroSeconds(r.ts-s[l].ts):void 0;if(u.clusterCumulativeScore+=r.args.data?r.args.data.weighted_score_delta:0,!r.args.data)continue;const g=e.SyntheticEvents.SyntheticEventsManager.registerSyntheticBasedEvent({rawSourceEvent:r,...r,args:{frame:r.args.frame,data:{...r.args.data,rawEvent:r}},parsedData:{timeFromNavigation:f,cumulativeWeightedScoreInWindow:u.clusterCumulativeScore,sessionWindowData:{cumulativeWindowScore:0,id:cn.length}}});u.events.push(g),un(u.clusterWindow,r.ts),c=r.ts,d=l}for(const e of cn){let r=0,i=-1;if(e===cn[cn.length-1]){const r=Kt+e.clusterWindow.min,i=e.clusterWindow.max+Qt,o=n.ArrayUtilities.nearestIndexFromBeginning(s,(t=>t.ts>e.clusterWindow.max)),c=o?s[o].ts:1/0,d=Math.min(r,i,a.max,c);un(e.clusterWindow,t.Timing.MicroSeconds(d))}for(const n of e.events){r+=n.args.data?n.args.data.weighted_score_delta:0,i=n.parsedData.sessionWindowData.id;const a=n.ts;n.parsedData.sessionWindowData.cumulativeWindowScore=e.clusterCumulativeScore,r<.1?un(e.scoreWindows.good,a):r>=.1&&r<.25?(e.scoreWindows.needsImprovement||(un(e.scoreWindows.good,t.Timing.MicroSeconds(a-1)),e.scoreWindows.needsImprovement=mn(a)),un(e.scoreWindows.needsImprovement,a)):r>=.25&&(e.scoreWindows.bad||(e.scoreWindows.needsImprovement?un(e.scoreWindows.needsImprovement,t.Timing.MicroSeconds(a-1)):un(e.scoreWindows.good,t.Timing.MicroSeconds(a-1)),e.scoreWindows.bad=mn(n.ts)),un(e.scoreWindows.bad,a)),e.scoreWindows.bad?un(e.scoreWindows.bad,e.clusterWindow.max):e.scoreWindows.needsImprovement?un(e.scoreWindows.needsImprovement,e.clusterWindow.max):un(e.scoreWindows.good,e.clusterWindow.max)}r>sn&&(on=i,sn=r)}}(),function(){const{traceBounds:e}=fe();dn.push({ts:e.min,score:0});for(const e of cn){let t=0;e.events[0].args.data&&dn.push({ts:e.clusterWindow.min,score:e.events[0].args.data.weighted_score_delta});for(let n=0;n<e.events.length;n++){const r=e.events[n];r.args.data&&(t+=r.args.data.weighted_score_delta,dn.push({ts:r.ts,score:t}))}dn.push({ts:e.clusterWindow.max,score:0})}}(),function(){rn.clear();for(const e of Jt)if(e.args.data?.impacted_nodes)for(const t of e.args.data.impacted_nodes)rn.add(t.node_id);for(const e of Zt)e.args.data?.nodeId&&rn.add(e.args.data.nodeId);for(const e of en)e.args.data?.nodeId&&rn.add(e.args.data.nodeId)}(),ln=3},data:function(){if(3!==ln)throw new Error("Layout Shifts Handler is not finalized");return{clusters:cn,sessionMaxScore:sn,clsWindowID:on,prePaintEvents:an,layoutInvalidationEvents:Zt,scheduleStyleInvalidationEvents:en,styleRecalcInvalidationEvents:[],renderFrameImplCreateChildFrameEvents:nn,scoreRecords:dn,backendNodeIds:[...rn]}},deps:function(){return["Screenshots","Meta"]},scoreClassificationForLayoutShift:function(e){let t="good";return e>=.1&&(t="ok"),e>=.25&&(t="bad"),t}});const gn=new Map;var Tn=Object.freeze({__proto__:null,reset:function(){gn.clear()},handleEvent:function(e){if(t.TraceEvents.isTraceEventUpdateCounters(e)){const t=n.MapUtilities.getWithDefault(gn,e.pid,(()=>[]));t.push(e),gn.set(e.pid,t)}},data:function(){return{updateCountersByProcess:gn}}});const hn=1e3,vn=1e6,pn=new Map,En=new Map,wn=new Map,Mn=[],In=new Map,Sn=new Map;function yn(e,t,n){En.has(e)||En.set(e,{});const r=En.get(e);if(!r)throw new Error(`Unable to locate trace events for request ID ${e}`);if(Array.isArray(r[t])){const e=n;r[t].push(...e)}else r[t]=n}function Fn(e){for(const t of e)if(t>0)return t;return 0}let Pn=1;var _n=Object.freeze({__proto__:null,reset:function(){wn.clear(),En.clear(),Mn.length=0,In.clear(),Sn.clear(),pn.clear(),Pn=1},initialize:function(){Pn=2},handleEvent:function(e){if(2!==Pn)throw new Error("Network Request handler is not initialized");if(t.TraceEvents.isTraceEventResourceChangePriority(e))yn(e.args.data.requestId,"changePriority",e);else if(t.TraceEvents.isTraceEventResourceWillSendRequest(e))yn(e.args.data.requestId,"willSendRequests",[e]);else if(t.TraceEvents.isTraceEventResourceSendRequest(e))yn(e.args.data.requestId,"sendRequests",[e]);else if(t.TraceEvents.isTraceEventResourceReceiveResponse(e))yn(e.args.data.requestId,"receiveResponse",e);else if(t.TraceEvents.isTraceEventResourceReceivedData(e))yn(e.args.data.requestId,"receivedData",[e]);else if(t.TraceEvents.isTraceEventResourceFinish(e))yn(e.args.data.requestId,"resourceFinish",e);else if(t.TraceEvents.isTraceEventResourceMarkAsCached(e))yn(e.args.data.requestId,"resourceMarkAsCached",e);else if(t.TraceEvents.isTraceEventWebSocketCreate(e)||t.TraceEvents.isTraceEventWebSocketInfo(e)||t.TraceEvents.isTraceEventWebSocketTransfer(e)){const t=e.args.data.identifier;pn.has(t)||(e.args.data.frame?pn.set(t,{frame:e.args.data.frame,webSocketIdentifier:t,events:[],syntheticConnectionEvent:null}):e.args.data.workerId&&pn.set(t,{workerId:e.args.data.workerId,webSocketIdentifier:t,events:[],syntheticConnectionEvent:null})),pn.get(t)?.events.push(e)}},finalize:async function(){if(2!==Pn)throw new Error("Network Request handler is not initialized");const{rendererProcessesByFrame:r}=fe();for(const[i,a]of En.entries()){if(!a.sendRequests||!a.receiveResponse)continue;const s=[];for(let e=0;e<a.sendRequests.length-1;e++){const n=a.sendRequests[e],r=a.sendRequests[e+1];let i=n.ts,o=t.Timing.MicroSeconds(r.ts-n.ts);if(a.willSendRequests&&a.willSendRequests[e]&&a.willSendRequests[e+1]){const n=a.willSendRequests[e],r=a.willSendRequests[e+1];i=n.ts,o=t.Timing.MicroSeconds(r.ts-n.ts)}s.push({url:n.args.data.url,priority:n.args.data.priority,requestMethod:n.args.data.requestMethod,ts:i,dur:o})}const o=0!==a.resourceFinish?.args.data.encodedDataLength,c=a.receiveResponse.args.data.fromCache&&!a.receiveResponse.args.data.fromServiceWorker&&!o,d=void 0!==a.resourceMarkAsCached,l=d||c,m=a.receiveResponse.args.data.timing;if(!m&&!l)continue;const u=a.sendRequests[0],f=a.sendRequests[a.sendRequests.length-1],g=f.args.data.priority;let T=g;a.changePriority&&(T=a.changePriority.args.data.priority);const h=a.willSendRequests&&a.willSendRequests.length?t.Timing.MicroSeconds(a.willSendRequests[0].ts):t.Timing.MicroSeconds(u.ts),v=a.willSendRequests&&a.willSendRequests.length?t.Timing.MicroSeconds(a.willSendRequests[a.willSendRequests.length-1].ts):t.Timing.MicroSeconds(f.ts),p=a.resourceFinish?a.resourceFinish.ts:v,E=a.resourceFinish?.args.data.finishTime?t.Timing.MicroSeconds(a.resourceFinish.args.data.finishTime*vn):t.Timing.MicroSeconds(p),w=l?t.Timing.MicroSeconds(0):t.Timing.MicroSeconds((E||v)-v),M=t.Timing.MicroSeconds(p-(E||p)),I=t.Timing.MicroSeconds(v-h),S=l?t.Timing.MicroSeconds(0):t.Timing.MicroSeconds(n.NumberUtilities.clamp(m.requestTime*vn-v,0,Number.MAX_VALUE)),y=l?t.Timing.MicroSeconds(a.receiveResponse.ts-h):t.Timing.MicroSeconds(Fn([m.dnsStart*hn,m.connectStart*hn,m.sendStart*hn,a.receiveResponse.ts-v])),F=l?h:t.Timing.MicroSeconds(m.requestTime*vn+m.sendStart*hn),P=l?t.Timing.MicroSeconds(0):t.Timing.MicroSeconds((m.receiveHeadersEnd-m.sendEnd)*hn),_=l?h:t.Timing.MicroSeconds(m.requestTime*vn+m.receiveHeadersEnd*hn),C=l?t.Timing.MicroSeconds(p-a.receiveResponse.ts):t.Timing.MicroSeconds((E||_)-_),R=t.Timing.MicroSeconds(w+M),k=l?t.Timing.MicroSeconds(0):t.Timing.MicroSeconds((m.dnsEnd-m.dnsStart)*hn),L=l?t.Timing.MicroSeconds(0):t.Timing.MicroSeconds((m.sslEnd-m.sslStart)*hn),D=l?t.Timing.MicroSeconds(0):t.Timing.MicroSeconds((m.proxyEnd-m.proxyStart)*hn),b=l?t.Timing.MicroSeconds(0):t.Timing.MicroSeconds((m.sendEnd-m.sendStart)*hn),N=l?t.Timing.MicroSeconds(0):t.Timing.MicroSeconds((m.connectEnd-m.connectStart)*hn),{frame:z,url:W,renderBlocking:x}=f.args.data,{encodedDataLength:O,decodedBodyLength:B}=a.resourceFinish?a.resourceFinish.args.data:{encodedDataLength:0,decodedBodyLength:0},U=new URL(W),q="https:"===U.protocol,H=e.Trace.activeURLForFrameAtTime(z,f.ts,r)||"",A=e.SyntheticEvents.SyntheticEventsManager.registerSyntheticBasedEvent({rawSourceEvent:f,args:{data:{syntheticData:{dnsLookup:k,download:C,downloadStart:_,finishTime:E,initialConnection:N,isDiskCached:c,isHttps:q,isMemoryCached:d,isPushedResource:o,networkDuration:w,processingDuration:M,proxyNegotiation:D,queueing:S,redirectionDuration:I,requestSent:b,sendStartTime:F,ssl:L,stalled:y,totalTime:R,waiting:P},decodedBodyLength:B,encodedDataLength:O,frame:z,fromServiceWorker:a.receiveResponse.args.data.fromServiceWorker,isLinkPreload:f.args.data.isLinkPreload||!1,mimeType:a.receiveResponse.args.data.mimeType,priority:T,initialPriority:g,protocol:a.receiveResponse.args.data.protocol??"unknown",redirects:s,renderBlocking:x??"non_blocking",requestId:i,requestingFrameUrl:H,requestMethod:f.args.data.requestMethod,resourceType:f.args.data.resourceType,statusCode:a.receiveResponse.args.data.statusCode,responseHeaders:a.receiveResponse.args.data.headers||[],fetchPriorityHint:f.args.data.fetchPriorityHint,initiator:f.args.data.initiator,stackTrace:f.args.data.stackTrace,timing:m,url:W,failed:a.resourceFinish?.args.data.didFail??!1,finished:Boolean(a.resourceFinish),connectionId:a.receiveResponse.args.data.connectionId,connectionReused:a.receiveResponse.args.data.connectionReused}},cat:"loading",name:"SyntheticNetworkRequest",ph:"X",dur:t.Timing.MicroSeconds(p-h),tdur:t.Timing.MicroSeconds(p-h),ts:t.Timing.MicroSeconds(h),tts:t.Timing.MicroSeconds(h),pid:f.pid,tid:f.tid}),j=n.MapUtilities.getWithDefault(wn,U.host,(()=>({renderBlocking:[],nonRenderBlocking:[],all:[]})));e.Network.isSyntheticNetworkRequestEventRenderBlocking(A)?j.renderBlocking.push(A):j.nonRenderBlocking.push(A),j.all.push(A),Mn.push(A);const G=A.args.data.initiator?.url||e.Trace.getZeroIndexedStackTraceForEvent(A)?.at(0)?.url;if(G){const e=In.get(G)??[];e.push(A),In.set(G,e)}}for(const e of Mn){const t=In.get(e.args.data.url);if(t)for(const n of t)Sn.set(n,e)}pn.forEach((e=>{let n=null,r=null;for(const i of e.events)t.TraceEvents.isTraceEventWebSocketCreate(i)&&(n=i),t.TraceEvents.isTraceEventWebSocketDestroy(i)&&(r=i);e.syntheticConnectionEvent=function(e,t,n){const{traceBounds:r}=fe(),i=e?e.ts:r.min,a=t?t.ts:r.max,s=a-i,o=e||t||n;return{name:"SyntheticWebSocketConnectionEvent",cat:o.cat,ph:"X",ts:i,dur:s,pid:o.pid,tid:o.tid,s:o.s,rawSourceEvent:o,_tag:"SyntheticEntryTag",args:{data:{identifier:o.args.data.identifier,priority:"Low",url:o.args.data.url||""}}}}(n,r,e.events[0])})),Pn=3},data:function(){if(3!==Pn)throw new Error("Network Request handler is not finalized");return{byOrigin:wn,byTime:Mn,eventToInitiator:Sn,webSocket:[...pn.values()]}},deps:function(){return["Meta"]}});const Cn=new Map;var Rn=Object.freeze({__proto__:null,reset:function(){Cn.clear()},handleEvent:function(e){if(t.TraceEvents.isTraceEventTracingStartedInBrowser(e))for(const t of e.args.data?.frames??[])Cn.set(t.frame,t);else if(t.TraceEvents.isTraceEventCommitLoad(e)){const t=e.args.data;if(!t)return;const n=Cn.get(t.frame);if(!n)return;Cn.set(t.frame,{...n,url:t.url||n.url,name:t.name||t.name})}},data:function(){return{frames:Cn}}});const kn=new Map;let Ln=[];let Dn=[];const bn=new Set;function Nn(r,i){const a=r.args.data?.navigationId;if(!a)throw new Error("Navigation event unexpectedly had no navigation ID.");const s=Wn(i),{rendererProcessesByFrame:o}=fe(),c=o.get(s);if(!c)return;if(c.get(i.pid)&&!t.TraceEvents.isTraceEventNavigationStart(i))if(t.TraceEvents.isTraceEventFirstContentfulPaint(i)){const e=t.Timing.MicroSeconds(i.ts-r.ts);zn(s,a,{event:i,metricName:"FCP",classification:On(e),navigation:r,timing:e})}else if(t.TraceEvents.isTraceEventFirstPaint(i)){zn(s,a,{event:i,metricName:"FP",classification:"unclassified",navigation:r,timing:t.Timing.MicroSeconds(i.ts-r.ts)})}else if(t.TraceEvents.isTraceEventMarkDOMContent(i)){const e=t.Timing.MicroSeconds(i.ts-r.ts);zn(s,a,{event:i,metricName:"DCL",classification:"unclassified",navigation:r,timing:e})}else if(t.TraceEvents.isTraceEventInteractiveTime(i)){const n=t.Timing.MicroSeconds(i.ts-r.ts);zn(s,a,{event:i,metricName:"TTI",classification:Bn(n),navigation:r,timing:n});const o=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(i.args.args.total_blocking_time_ms));zn(s,a,{event:i,metricName:"TBT",classification:Hn(o),navigation:r,timing:o})}else if(t.TraceEvents.isTraceEventMarkLoad(i)){zn(s,a,{event:i,metricName:"L",classification:"unclassified",navigation:r,timing:t.Timing.MicroSeconds(i.ts-r.ts)})}else if(t.TraceEvents.isTraceEventLargestContentfulPaintCandidate(i)){const e=i.args.data?.candidateIndex;if(!e)throw new Error("Largest Contenful Paint unexpectedly had no candidateIndex.");const o=t.Timing.MicroSeconds(i.ts-r.ts),c={event:i,metricName:"LCP",classification:Un(o),navigation:r,timing:o},d=n.MapUtilities.getWithDefault(kn,s,(()=>new Map)),l=n.MapUtilities.getWithDefault(d,a,(()=>new Map)).get("LCP");if(void 0===l)return bn.add(c.event),void zn(s,a,c);const m=l.event;if(!t.TraceEvents.isTraceEventLargestContentfulPaintCandidate(m))return;const u=m.args.data?.candidateIndex;if(!u)return;u<e&&(bn.delete(m),bn.add(c.event),zn(s,a,c))}else if(!t.TraceEvents.isTraceEventLayoutShift(i))return n.assertNever(i,`Unexpected event type: ${i}`)}function zn(e,t,r){const i=n.MapUtilities.getWithDefault(kn,e,(()=>new Map)),a=n.MapUtilities.getWithDefault(i,t,(()=>new Map));a.delete(r.metricName),a.set(r.metricName,r)}function Wn(e){if(t.TraceEvents.isTraceEventFirstContentfulPaint(e)||t.TraceEvents.isTraceEventInteractiveTime(e)||t.TraceEvents.isTraceEventLargestContentfulPaintCandidate(e)||t.TraceEvents.isTraceEventNavigationStart(e)||t.TraceEvents.isTraceEventLayoutShift(e)||t.TraceEvents.isTraceEventFirstPaint(e))return e.args.frame;if(t.TraceEvents.isTraceEventMarkDOMContent(e)||t.TraceEvents.isTraceEventMarkLoad(e)){const t=e.args.data?.frame;if(!t)throw new Error("MarkDOMContent unexpectedly had no frame ID.");return t}n.assertNever(e,`Unexpected event type: ${e}`)}function xn(r){if(t.TraceEvents.isTraceEventFirstContentfulPaint(r)||t.TraceEvents.isTraceEventLargestContentfulPaintCandidate(r)||t.TraceEvents.isTraceEventFirstPaint(r)){const e=r.args.data?.navigationId;if(!e)throw new Error("Trace event unexpectedly had no navigation ID.");const{navigationsByNavigationId:t}=fe(),n=t.get(e);return n||null}if(t.TraceEvents.isTraceEventMarkDOMContent(r)||t.TraceEvents.isTraceEventInteractiveTime(r)||t.TraceEvents.isTraceEventLayoutShift(r)||t.TraceEvents.isTraceEventMarkLoad(r)){const t=Wn(r),{navigationsByFrameId:n}=fe();return e.Trace.getNavigationForTraceEvent(r,t,n)}return t.TraceEvents.isTraceEventNavigationStart(r)?null:n.assertNever(r,`Unexpected event type: ${r}`)}function On(n){const r=e.Timing.secondsToMicroseconds(t.Timing.Seconds(1.8));let i="bad";return n<=e.Timing.secondsToMicroseconds(t.Timing.Seconds(3))&&(i="ok"),n<=r&&(i="good"),i}function Bn(n){const r=e.Timing.secondsToMicroseconds(t.Timing.Seconds(3.8));let i="bad";return n<=e.Timing.secondsToMicroseconds(t.Timing.Seconds(7.3))&&(i="ok"),n<=r&&(i="good"),i}function Un(n){const r=e.Timing.secondsToMicroseconds(t.Timing.Seconds(2.5));let i="bad";return n<=e.Timing.secondsToMicroseconds(t.Timing.Seconds(4))&&(i="ok"),n<=r&&(i="good"),i}function qn(e){return"unclassified"}function Hn(n){const r=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(200));let i="bad";return n<=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(600))&&(i="ok"),n<=r&&(i="good"),i}var An=Object.freeze({__proto__:null,reset:function(){kn.clear(),Dn=[],Ln=[],bn.clear()},handleEvent:function(e){t.TraceEvents.eventIsPageLoadEvent(e)&&Dn.push(e)},getFrameIdForPageLoadEvent:Wn,scoreClassificationForFirstContentfulPaint:On,scoreClassificationForTimeToInteractive:Bn,scoreClassificationForLargestContentfulPaint:Un,scoreClassificationForDOMContentLoaded:qn,scoreClassificationForTotalBlockingTime:Hn,finalize:async function(){Dn.sort(((e,t)=>e.ts-t.ts));for(const e of Dn){const t=xn(e);t&&Nn(t,e)}const e=function(){const e=[],t=[...kn.values()].flatMap((e=>[...e.values()]));for(let n=0;n<t.length;n++){const r=t[n].get("LCP");r&&r.event&&e.push(r.event)}return e}(),n=fe().mainFrameId,r=[...e,...Dn.filter((e=>!t.TraceEvents.isTraceEventLargestContentfulPaintCandidate(e)))].filter(t.TraceEvents.isTraceEventMarkerEvent);Ln=r.filter((e=>Wn(e)===n)).sort(((e,t)=>e.ts-t.ts))},data:function(){return{metricScoresByFrameId:kn,allMarkerEvents:Ln}},deps:function(){return["Meta"]}});const jn=[],Gn=[],Vn=[];let $n={};function Xn(e){const t=parseInt(e.id,16);if(1===t)return e.ts;return $n[t]??e.ts}var Yn=Object.freeze({__proto__:null,reset:function(){jn.length=0,Gn.length=0,Vn.length=0,$n={}},handleEvent:function(e){t.TraceEvents.isTraceEventScreenshot(e)?Gn.push(e):t.TraceEvents.isTraceEventPipelineReporter(e)&&jn.push(e)},finalize:async function(){const n=e.Trace.createMatchedSortedSyntheticEvents(jn);$n=Object.fromEntries(n.map((e=>[e.args.data.beginEvent.args.chrome_frame_reporter.frame_sequence,t.Timing.MicroSeconds(e.ts+e.dur)])));for(const t of Gn){const{cat:n,name:r,ph:i,pid:a,tid:s}=t,o=e.SyntheticEvents.SyntheticEventsManager.registerSyntheticBasedEvent({rawSourceEvent:t,cat:n,name:r,ph:i,pid:a,tid:s,ts:Xn(t),args:{dataUri:`data:image/jpg;base64,${t.args.snapshot}`}});Vn.push(o)}},data:function(){return Vn},deps:function(){return["Meta"]}});let Kn=null;const Qn=new Map;var Jn=Object.freeze({__proto__:null,reset:function(){Kn=null,Qn.clear()},handleEvent:function(e){t.TraceEvents.isTraceEventSelectorStats(e)&&Kn&&e.args.selector_stats?Qn.set(Kn,{timings:e.args.selector_stats.selector_timings}):t.TraceEvents.isTraceEventUpdateLayoutTree(e)&&(Kn=e)},data:function(){return{dataForUpdateLayoutEvent:Qn}}});const Zn=[],er=[],tr=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(200)),nr=tr,rr=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(500));let ir=null;const ar=[],sr=[],or=new Map,cr=[];let dr=1;const lr=new Set(["pointerdown","touchstart","pointerup","touchend","mousedown","mouseup","click"]),mr=new Set(["keydown","keypress","keyup"]);function ur(e){return lr.has(e.type)?"POINTER":mr.has(e.type)?"KEYBOARD":"OTHER"}function fr(e){const n={POINTER:new Map,KEYBOARD:new Map,OTHER:new Map};function r(e){const r=ur(e),i=n[r],a=t.Timing.MicroSeconds(e.ts+e.dur),s=i.get(a);if(s){if(e.ts<s.ts)i.set(a,e);else if(e.ts===s.ts&&e.interactionId===s.interactionId){const t=s.processingEnd-s.processingStart;e.processingEnd-e.processingStart>t&&i.set(a,e)}e.processingStart<s.processingStart&&(s.processingStart=e.processingStart,gr(s)),e.processingEnd>s.processingEnd&&(s.processingEnd=e.processingEnd,gr(s))}else i.set(a,e)}for(const t of e)r(t);const i=Object.values(n).flatMap((e=>Array.from(e.values())));return i.sort(((e,t)=>e.ts-t.ts)),i}function gr(e){const n=e.args.data.beginEvent,r=e.args.data.endEvent;e.inputDelay=t.Timing.MicroSeconds(e.processingStart-n.ts),e.mainThreadHandling=t.Timing.MicroSeconds(e.processingEnd-e.processingStart),e.presentationDelay=t.Timing.MicroSeconds(r.ts-e.processingEnd)}function Tr(){return{allEvents:Zn,beginCommitCompositorFrameEvents:er,interactionEvents:ar,interactionEventsWithNoNesting:sr,longestInteractionEvent:ir,interactionsOverThreshold:new Set(ar.filter((e=>e.dur>tr)))}}var hr=Object.freeze({__proto__:null,LONG_INTERACTION_THRESHOLD:tr,reset:function(){Zn.length=0,er.length=0,ar.length=0,cr.length=0,or.clear(),sr.length=0,ir=null,dr=2},handleEvent:function(e){if(2!==dr)throw new Error("Handler is not initialized");if(t.TraceEvents.isTraceEventBeginCommitCompositorFrame(e))return void er.push(e);if(!t.TraceEvents.isTraceEventEventTiming(e))return;if(t.TraceEvents.isTraceEventEventTimingEnd(e)&&or.set(e.id,e),Zn.push(e),!e.args.data||!t.TraceEvents.isTraceEventEventTimingStart(e))return;const{duration:n,interactionId:r}=e.args.data;n<1||void 0===r||0===r||cr.push(e)},categoryOfInteraction:ur,removeNestedInteractions:fr,finalize:async function(){const{navigationsByFrameId:n}=fe();for(const r of cr){const i=or.get(r.id);if(!i)continue;if(!r.args.data?.type||!r.args.data?.interactionId)continue;const a=t.Timing.MicroSeconds(e.Timing.millisecondsToMicroseconds(r.args.data.processingStart)-e.Timing.millisecondsToMicroseconds(r.args.data.timeStamp)+r.ts),s=t.Timing.MicroSeconds(e.Timing.millisecondsToMicroseconds(r.args.data.processingEnd)-e.Timing.millisecondsToMicroseconds(r.args.data.timeStamp)+r.ts),o=r.args.frame??r.args.data.frame,c=e.Trace.getNavigationForTraceEvent(r,o,n),d=c?.args.data?.navigationId,l=e.SyntheticEvents.SyntheticEventsManager.registerSyntheticBasedEvent({rawSourceEvent:r,cat:r.cat,name:r.name,pid:r.pid,tid:r.tid,ph:r.ph,processingStart:a,processingEnd:s,inputDelay:t.Timing.MicroSeconds(-1),mainThreadHandling:t.Timing.MicroSeconds(-1),presentationDelay:t.Timing.MicroSeconds(-1),args:{data:{beginEvent:r,endEvent:i,frame:o,navigationId:d}},ts:r.ts,dur:t.Timing.MicroSeconds(i.ts-r.ts),type:r.args.data.type,interactionId:r.args.data.interactionId});gr(l),ar.push(l)}dr=3,sr.push(...fr(ar));for(const e of sr)(!ir||ir.dur<e.dur)&&(ir=e)},data:Tr,deps:function(){return["Meta"]},scoreClassificationForInteractionToNextPaint:function(e){return e<=nr?"good":e<=rr?"ok":"bad"}});const vr=new Map,pr=new Map,Er=[],wr=[],Mr=[],Ir=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(30)),Sr=e.Timing.millisecondsToMicroseconds(t.Timing.MilliSeconds(50));function yr(e,t){const r=n.MapUtilities.getWithDefault(vr,e,(()=>[]));r.push(t),vr.set(e,r);const i=n.MapUtilities.getWithDefault(pr,t,(()=>[]));i.push(e),pr.set(t,i)}function Fr(e,t,n=!0){let r=t.at(-1);for(;r&&e.ts>r.ts+(r.dur||0);)t.pop(),r=t.at(-1);n&&t.push(e)}var Pr=Object.freeze({__proto__:null,FORCED_REFLOW_THRESHOLD:Ir,LONG_MAIN_THREAD_TASK_THRESHOLD:Sr,reset:function(){vr.clear(),pr.clear(),Er.length=0,wr.length=0,Mr.length=0},handleEvent:function(n){if(function(e){if(Fr(e,Er),Fr(e,wr,t.TraceEvents.isJSInvocationEvent(e)),wr.length&&("Layout"===e.name||"UpdateLayoutTree"===e.name))return void Mr.push(e);if(1===Er.length){const e=Mr.reduce(((e,t)=>e+(t.dur||0)),0);e>=Ir&&Mr.forEach((e=>yr(e,"FORCED_REFLOW"))),Mr.length=0}}(n),"RunTask"!==n.name)if(t.TraceEvents.isTraceEventFireIdleCallback(n)){const{duration:t}=e.Timing.eventTimingsMilliSeconds(n);t>n.args.data.allottedMilliseconds&&yr(n,"IDLE_CALLBACK_OVER_TIME")}else;else{const{duration:t}=e.Timing.eventTimingsMicroSeconds(n);t>Sr&&yr(n,"LONG_TASK")}},deps:function(){return["UserInteractions"]},finalize:async function(){const e=Tr().interactionsOverThreshold;for(const t of e)yr(t,"LONG_INTERACTION")},data:function(){return{perEvent:vr,perWarning:pr}}});let _r=1;const Cr=[],Rr=new Map,kr=new Map;var Lr=Object.freeze({__proto__:null,initialize:function(){if(1!==_r)throw new Error("Workers Handler was not reset");_r=2},reset:function(){Cr.length=0,Rr.clear(),kr.clear(),_r=1},handleEvent:function(e){if(2!==_r)throw new Error("Workers Handler is not initialized");t.TraceEvents.isTraceEventTracingSessionIdForWorker(e)&&Cr.push(e)},finalize:async function(){if(2!==_r)throw new Error("Handler is not initialized");for(const e of Cr)e.args.data&&(Rr.set(e.args.data.workerThreadId,e.args.data.workerId),kr.set(e.args.data.workerId,e.args.data.url));_r=3},data:function(){if(3!==_r)throw new Error("Workers Handler is not finalized");return{workerSessionIdEvents:Cr,workerIdByThread:Rr,workerURLById:kr}}}),Dr=Object.freeze({__proto__:null,Animations:c,AuctionWorklets:v,EnhancedTraces:_,ExtensionTraceData:j,Frames:Tt,GPU:Et,ImagePainting:yt,Initiators:Ot,Invalidations:Gt,LargestImagePaint:$t,LargestTextPaint:Yt,LayerTree:ye,LayoutShifts:fn,Memory:Tn,Meta:ge,NetworkRequests:_n,PageFrames:Rn,PageLoadMetrics:An,Renderer:tt,Samples:De,Screenshots:Yn,SelectorStats:Jn,UserInteractions:hr,UserTimings:x,Warnings:Pr,Workers:Lr}),br=Object.freeze({__proto__:null});export{Dr as ModelHandlers,it as Threads,br as Types};
