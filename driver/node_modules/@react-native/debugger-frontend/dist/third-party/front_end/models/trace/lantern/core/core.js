class e extends Error{}class t{static rewriteChromeInternalUrl(e){return e&&e.startsWith("chrome://")?(e.endsWith("/")&&(e=e.replace(/\/$/,"")),e.replace(/^chrome:\/\/chrome\//,"chrome://")):e}static equalWithExcludedFragments(e,t){[e,t]=[e,t].map(this.rewriteChromeInternalUrl);try{const n=new URL(e);n.hash="";const r=new URL(t);return r.hash="",n.href===r.href}catch(e){return!1}}}const n={Document:.9,XHR:.9,Fetch:.9};class r{static get summary(){return"__SUMMARY__"}static groupByOrigin(e){const t=new Map;return e.forEach((e=>{const n=e.parsedURL.securityOrigin,r=t.get(n)||[];r.push(e),t.set(n,r)})),t}static getSummary(e){let t;if(e.sort(((e,t)=>e-t)),0===e.length)t=e[0];else if(e.length%2==0){t=(e[Math.floor((e.length-1)/2)]+e[Math.floor((e.length-1)/2)+1])/2}else t=e[Math.floor((e.length-1)/2)];return{min:e[0],max:e[e.length-1],avg:e.reduce(((e,t)=>e+t),0)/e.length,median:t}}static summarize(e){const t=new Map,n=[];for(const[i,s]of e)t.set(i,r.getSummary(s)),n.push(...s);return t.set(r.summary,r.getSummary(n)),t}static _estimateValueByOrigin(e,t){const n=r.estimateIfConnectionWasReused(e),i=r.groupByOrigin(e),s=new Map;for(const[e,r]of i.entries()){let i=[];for(const e of r){const r=e.timing;if(!r)continue;const s=t({request:e,timing:r,connectionReused:n.get(e.requestId)});void 0!==s&&(i=i.concat(s))}i.length&&s.set(e,i)}return s}static _estimateRTTViaConnectionTiming(e){const{timing:t,connectionReused:n,request:r}=e;if(n)return;const{connectStart:i,sslStart:s,sslEnd:o,connectEnd:a}=t;return a>=0&&i>=0&&r.protocol.startsWith("h3")?a-i:s>=0&&o>=0&&s!==i?[a-s,s-i]:i>=0&&a>=0?a-i:void 0}static _estimateRTTViaDownloadTiming(e){const{timing:t,connectionReused:n,request:r}=e;if(n)return;if(r.transferSize<=14336)return;if(!Number.isFinite(t.receiveHeadersEnd)||t.receiveHeadersEnd<0)return;const i=r.networkEndTime-r.networkRequestTime-t.receiveHeadersEnd,s=Math.log2(r.transferSize/14336);return s>5?void 0:i/s}static _estimateRTTViaSendStartTiming(e){const{timing:t,connectionReused:n,request:r}=e;if(n)return;if(!Number.isFinite(t.sendStart)||t.sendStart<0)return;let i=1;return r.protocol.startsWith("h3")||(i+=1),"https"===r.parsedURL.scheme&&(i+=1),t.sendStart/i}static _estimateRTTViaHeadersEndTiming(e){const{timing:t,connectionReused:r,request:i}=e;if(!Number.isFinite(t.receiveHeadersEnd)||t.receiveHeadersEnd<0)return;if(!i.resourceType)return;const s=n[i.resourceType]||.4,o=t.receiveHeadersEnd*s;let a=1;return r||(a+=1,i.protocol.startsWith("h3")||(a+=1),"https"===i.parsedURL.scheme&&(a+=1)),Math.max((t.receiveHeadersEnd-o)/a,3)}static _estimateResponseTimeByOrigin(e,t){return r._estimateValueByOrigin(e,(({request:e,timing:n})=>{if(void 0!==e.serverResponseTime)return e.serverResponseTime;if(!Number.isFinite(n.receiveHeadersEnd)||n.receiveHeadersEnd<0)return;if(!Number.isFinite(n.sendEnd)||n.sendEnd<0)return;const i=n.receiveHeadersEnd-n.sendEnd,s=e.parsedURL.securityOrigin,o=t.get(s)||t.get(r.summary)||0;return Math.max(i-o,0)}))}static canTrustConnectionInformation(e){const t=new Map;for(const n of e){const e=t.get(n.connectionId)||!n.connectionReused;t.set(n.connectionId,e)}return!(t.size<=1)&&Array.from(t.values()).every((e=>e))}static estimateIfConnectionWasReused(e,t){const{forceCoarseEstimates:n=!1}=t||{};if(!n&&r.canTrustConnectionInformation(e))return new Map(e.map((e=>[e.requestId,Boolean(e.connectionReused)])));const i=new Map,s=r.groupByOrigin(e);for(const e of s.values()){const t=e.map((e=>e.networkEndTime)).reduce(((e,t)=>Math.min(e,t)),1/0);for(const n of e)i.set(n.requestId,n.networkRequestTime>=t||"h2"===n.protocol);const n=e.reduce(((e,t)=>e.networkRequestTime>t.networkRequestTime?t:e));i.set(n.requestId,!1)}return i}static estimateRTTByOrigin(t,n){const{forceCoarseEstimates:i=!1,coarseEstimateMultiplier:s=.3,useDownloadEstimates:o=!0,useSendStartEstimates:a=!0,useHeadersEndEstimates:c=!0}=n||{},u=r.estimateIfConnectionWasReused(t),m=r.groupByOrigin(t),d=new Map;for(const[h,g]of m.entries()){const f=[];function l(e,t=1){for(const n of g){const r=n.timing;if(!r)continue;const i=e({request:n,timing:r,connectionReused:u.get(n.requestId)});void 0!==i&&(Array.isArray(i)?f.push(...i.map((e=>e*t))):f.push(i*t))}}i||l(this._estimateRTTViaConnectionTiming),f.length||(o&&l(this._estimateRTTViaDownloadTiming,s),a&&l(this._estimateRTTViaSendStartTiming,s),c&&l(this._estimateRTTViaHeadersEndTiming,s)),f.length&&d.set(h,f)}if(!d.size)throw new e("No timing information available");return r.summarize(d)}static estimateServerResponseTimeByOrigin(e,t){let n=(t||{}).rttByOrigin;if(!n){n=new Map;const i=r.estimateRTTByOrigin(e,t);for(const[e,t]of i.entries())n.set(e,t.min)}const i=r._estimateResponseTimeByOrigin(e,n);return r.summarize(i)}static estimateThroughput(e){let t=0;const n=e.reduce(((e,n)=>{const r=n.parsedURL?.scheme;return"data"===r||n.failed||!n.finished||n.statusCode>300||!n.transferSize||(t+=n.transferSize,e.push({time:n.responseHeadersEndTime/1e3,isStart:!0}),e.push({time:n.networkEndTime/1e3,isStart:!1})),e}),[]).sort(((e,t)=>e.time-t.time));if(!n.length)return 1/0;let r=0,i=0,s=0;return n.forEach((e=>{e.isStart?(0===r&&(i=e.time),r++):(r--,0===r&&(s+=e.time-i))})),8*t/s}static computeRTTAndServerResponseTime(e){const t=new Map;for(const[n,i]of r.estimateRTTByOrigin(e).entries())t.set(n,i.min);const n=Math.min(...Array.from(t.values())),i=r.estimateServerResponseTimeByOrigin(e,{rttByOrigin:t}),s=new Map,o=new Map;for(const[e,r]of i.entries()){const i=t.get(e)||n;s.set(e,i-n),o.set(e,r.median)}return{rtt:n,additionalRttByOrigin:s,serverResponseTimeByOrigin:o}}static analyze(e){return{throughput:r.estimateThroughput(e),...r.computeRTTAndServerResponseTime(e)}}static findResourceForUrl(e,n){return e.find((e=>n.startsWith(e.url)&&t.equalWithExcludedFragments(e.url,n)))}static findLastDocumentForUrl(e,n){const r=e.filter((e=>"Document"===e.resourceType&&n.startsWith(e.url)&&t.equalWithExcludedFragments(e.url,n)));return r[r.length-1]}static resolveRedirects(e){for(;e.redirectDestination;)e=e.redirectDestination;return e}}export{e as LanternError,r as NetworkAnalyzer};
