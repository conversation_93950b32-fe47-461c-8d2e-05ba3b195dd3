{"version": 3, "file": "IosConfig.types.js", "names": [], "sources": ["../../src/ios/IosConfig.types.ts"], "sourcesContent": ["import { JSONValue } from '@expo/json-file';\n\nexport type URLScheme = {\n  CFBundleURLName?: string;\n  CFBundleURLSchemes: string[];\n};\n\nexport type InterfaceOrientation =\n  | 'UIInterfaceOrientationPortrait'\n  | 'UIInterfaceOrientationPortraitUpsideDown'\n  | 'UIInterfaceOrientationLandscapeLeft'\n  | 'UIInterfaceOrientationLandscapeRight';\n\nexport type InterfaceStyle = 'Light' | 'Dark' | 'Automatic';\n\nexport type InfoPlist = Record<string, JSONValue | undefined> & {\n  UIStatusBarHidden?: boolean;\n  UIStatusBarStyle?: string;\n  UILaunchStoryboardName?: string | 'SplashScreen';\n  CFBundleShortVersionString?: string;\n  CFBundleVersion?: string;\n  CFBundleDisplayName?: string;\n  CFBundleIdentifier?: string;\n  CFBundleName?: string;\n  CFBundleURLTypes?: URLScheme[];\n  CFBundleDevelopmentRegion?: string;\n  ITSAppUsesNonExemptEncryption?: boolean;\n  LSApplicationQueriesSchemes?: string[];\n  UIBackgroundModes?: string[];\n  UISupportedInterfaceOrientations?: InterfaceOrientation[];\n  GMSApiKey?: string;\n  GADApplicationIdentifier?: string;\n  UIUserInterfaceStyle?: InterfaceStyle;\n  UIRequiresFullScreen?: boolean;\n  SKAdNetworkItems?: { SKAdNetworkIdentifier: string }[];\n  branch_key?: { live?: string };\n};\n\nexport type ExpoPlist = {\n  EXUpdatesCheckOnLaunch?: string;\n  EXUpdatesEnabled?: boolean;\n  EXUpdatesHasEmbeddedUpdate?: boolean;\n  EXUpdatesLaunchWaitMs?: number;\n  EXUpdatesRuntimeVersion?: string;\n  EXUpdatesRequestHeaders?: Record<string, string>;\n  /**\n   * @deprecated removed, but kept in types so that it can be mutated (deleted) from existing plists\n   */\n  EXUpdatesSDKVersion?: string;\n  EXUpdatesURL?: string;\n  EXUpdatesCodeSigningCertificate?: string;\n  EXUpdatesCodeSigningMetadata?: Record<string, string>;\n  EXUpdatesDisableAntiBrickingMeasures?: boolean;\n};\n"], "mappings": "", "ignoreList": []}