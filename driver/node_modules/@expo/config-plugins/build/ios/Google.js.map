{"version": 3, "file": "Google.js", "names": ["_plist", "data", "_interopRequireDefault", "require", "_assert", "_fs", "_path", "_Paths", "_Scheme", "_Xcodeproj", "_iosPlugins", "e", "__esModule", "default", "<PERSON><PERSON><PERSON><PERSON>", "config", "withInfoPlist", "modResults", "setGoogleConfig", "modRequest", "exports", "withGoogleServicesFile", "withXcodeProject", "setGoogleServicesFile", "projectRoot", "project", "readGoogleServicesInfoPlist", "relativePath", "googleServiceFilePath", "path", "resolve", "contents", "fs", "readFileSync", "assert", "plist", "parse", "getGoogleSignInReversedClientId", "googleServicesFileRelativePath", "getGoogleServicesFile", "infoPlist", "REVERSED_CLIENT_ID", "ios", "googleServicesFile", "setGoogleSignInReversedClientId", "reversedClientId", "appendScheme", "copyFileSync", "join", "getSourceRoot", "projectName", "getProjectName", "plist<PERSON><PERSON><PERSON><PERSON>", "hasFile", "addResourceFileToGroup", "filepath", "groupName", "isBuildFile", "verbose"], "sources": ["../../src/ios/Google.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\nimport plist from '@expo/plist';\nimport assert from 'assert';\nimport fs from 'fs';\nimport path from 'path';\nimport { XcodeProject } from 'xcode';\n\nimport { InfoPlist } from './IosConfig.types';\nimport { getSourceRoot } from './Paths';\nimport { appendScheme } from './Scheme';\nimport { ConfigPlugin, ModProps } from '../Plugin.types';\nimport { addResourceFileToGroup, getProjectName } from './utils/Xcodeproj';\nimport { withInfoPlist, withXcodeProject } from '../plugins/ios-plugins';\n\nexport const withGoogle: ConfigPlugin = (config) => {\n  return withInfoPlist(config, (config) => {\n    config.modResults = setGoogleConfig(config, config.modResults, config.modRequest);\n    return config;\n  });\n};\n\nexport const withGoogleServicesFile: ConfigPlugin = (config) => {\n  return withXcodeProject(config, (config) => {\n    config.modResults = setGoogleServicesFile(config, {\n      projectRoot: config.modRequest.projectRoot,\n      project: config.modResults,\n    });\n    return config;\n  });\n};\n\nfunction readGoogleServicesInfoPlist(\n  relativePath: string,\n  { projectRoot }: { projectRoot: string }\n) {\n  const googleServiceFilePath = path.resolve(projectRoot, relativePath);\n  const contents = fs.readFileSync(googleServiceFilePath, 'utf8');\n  assert(contents, 'GoogleService-Info.plist is empty');\n  return plist.parse(contents);\n}\n\nexport function getGoogleSignInReversedClientId(\n  config: Pick<ExpoConfig, 'ios'>,\n  modRequest: Pick<ModProps<InfoPlist>, 'projectRoot'>\n): string | null {\n  const googleServicesFileRelativePath = getGoogleServicesFile(config);\n  if (googleServicesFileRelativePath === null) {\n    return null;\n  }\n\n  const infoPlist = readGoogleServicesInfoPlist(googleServicesFileRelativePath, modRequest);\n\n  return infoPlist.REVERSED_CLIENT_ID ?? null;\n}\n\nexport function getGoogleServicesFile(config: Pick<ExpoConfig, 'ios'>) {\n  return config.ios?.googleServicesFile ?? null;\n}\n\nexport function setGoogleSignInReversedClientId(\n  config: Pick<ExpoConfig, 'ios'>,\n  infoPlist: InfoPlist,\n  modRequest: Pick<ModProps<InfoPlist>, 'projectRoot'>\n): InfoPlist {\n  const reversedClientId = getGoogleSignInReversedClientId(config, modRequest);\n\n  if (reversedClientId === null) {\n    return infoPlist;\n  }\n\n  return appendScheme(reversedClientId, infoPlist);\n}\n\nexport function setGoogleConfig(\n  config: Pick<ExpoConfig, 'ios'>,\n  infoPlist: InfoPlist,\n  modRequest: ModProps<InfoPlist>\n): InfoPlist {\n  infoPlist = setGoogleSignInReversedClientId(config, infoPlist, modRequest);\n  return infoPlist;\n}\n\nexport function setGoogleServicesFile(\n  config: Pick<ExpoConfig, 'ios'>,\n  { projectRoot, project }: { project: XcodeProject; projectRoot: string }\n): XcodeProject {\n  const googleServicesFileRelativePath = getGoogleServicesFile(config);\n  if (googleServicesFileRelativePath === null) {\n    return project;\n  }\n\n  const googleServiceFilePath = path.resolve(projectRoot, googleServicesFileRelativePath);\n  fs.copyFileSync(\n    googleServiceFilePath,\n    path.join(getSourceRoot(projectRoot), 'GoogleService-Info.plist')\n  );\n\n  const projectName = getProjectName(projectRoot);\n  const plistFilePath = `${projectName}/GoogleService-Info.plist`;\n  if (!project.hasFile(plistFilePath)) {\n    project = addResourceFileToGroup({\n      filepath: plistFilePath,\n      groupName: projectName,\n      project,\n      isBuildFile: true,\n      verbose: true,\n    });\n  }\n  return project;\n}\n"], "mappings": ";;;;;;;;;;;AACA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,QAAA;EAAA,MAAAH,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAC,OAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,IAAA;EAAA,MAAAJ,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAE,GAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,MAAA;EAAA,MAAAL,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAG,KAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAIA,SAAAM,OAAA;EAAA,MAAAN,IAAA,GAAAE,OAAA;EAAAI,MAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,QAAA;EAAA,MAAAP,IAAA,GAAAE,OAAA;EAAAK,OAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAQ,WAAA;EAAA,MAAAR,IAAA,GAAAE,OAAA;EAAAM,UAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAS,YAAA;EAAA,MAAAT,IAAA,GAAAE,OAAA;EAAAO,WAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAyE,SAAAC,uBAAAS,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAElE,MAAMG,UAAwB,GAAIC,MAAM,IAAK;EAClD,OAAO,IAAAC,2BAAa,EAACD,MAAM,EAAGA,MAAM,IAAK;IACvCA,MAAM,CAACE,UAAU,GAAGC,eAAe,CAACH,MAAM,EAAEA,MAAM,CAACE,UAAU,EAAEF,MAAM,CAACI,UAAU,CAAC;IACjF,OAAOJ,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACK,OAAA,CAAAN,UAAA,GAAAA,UAAA;AAEK,MAAMO,sBAAoC,GAAIN,MAAM,IAAK;EAC9D,OAAO,IAAAO,8BAAgB,EAACP,MAAM,EAAGA,MAAM,IAAK;IAC1CA,MAAM,CAACE,UAAU,GAAGM,qBAAqB,CAACR,MAAM,EAAE;MAChDS,WAAW,EAAET,MAAM,CAACI,UAAU,CAACK,WAAW;MAC1CC,OAAO,EAAEV,MAAM,CAACE;IAClB,CAAC,CAAC;IACF,OAAOF,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACK,OAAA,CAAAC,sBAAA,GAAAA,sBAAA;AAEF,SAASK,2BAA2BA,CAClCC,YAAoB,EACpB;EAAEH;AAAqC,CAAC,EACxC;EACA,MAAMI,qBAAqB,GAAGC,eAAI,CAACC,OAAO,CAACN,WAAW,EAAEG,YAAY,CAAC;EACrE,MAAMI,QAAQ,GAAGC,aAAE,CAACC,YAAY,CAACL,qBAAqB,EAAE,MAAM,CAAC;EAC/D,IAAAM,iBAAM,EAACH,QAAQ,EAAE,mCAAmC,CAAC;EACrD,OAAOI,gBAAK,CAACC,KAAK,CAACL,QAAQ,CAAC;AAC9B;AAEO,SAASM,+BAA+BA,CAC7CtB,MAA+B,EAC/BI,UAAoD,EACrC;EACf,MAAMmB,8BAA8B,GAAGC,qBAAqB,CAACxB,MAAM,CAAC;EACpE,IAAIuB,8BAA8B,KAAK,IAAI,EAAE;IAC3C,OAAO,IAAI;EACb;EAEA,MAAME,SAAS,GAAGd,2BAA2B,CAACY,8BAA8B,EAAEnB,UAAU,CAAC;EAEzF,OAAOqB,SAAS,CAACC,kBAAkB,IAAI,IAAI;AAC7C;AAEO,SAASF,qBAAqBA,CAACxB,MAA+B,EAAE;EACrE,OAAOA,MAAM,CAAC2B,GAAG,EAAEC,kBAAkB,IAAI,IAAI;AAC/C;AAEO,SAASC,+BAA+BA,CAC7C7B,MAA+B,EAC/ByB,SAAoB,EACpBrB,UAAoD,EACzC;EACX,MAAM0B,gBAAgB,GAAGR,+BAA+B,CAACtB,MAAM,EAAEI,UAAU,CAAC;EAE5E,IAAI0B,gBAAgB,KAAK,IAAI,EAAE;IAC7B,OAAOL,SAAS;EAClB;EAEA,OAAO,IAAAM,sBAAY,EAACD,gBAAgB,EAAEL,SAAS,CAAC;AAClD;AAEO,SAAStB,eAAeA,CAC7BH,MAA+B,EAC/ByB,SAAoB,EACpBrB,UAA+B,EACpB;EACXqB,SAAS,GAAGI,+BAA+B,CAAC7B,MAAM,EAAEyB,SAAS,EAAErB,UAAU,CAAC;EAC1E,OAAOqB,SAAS;AAClB;AAEO,SAASjB,qBAAqBA,CACnCR,MAA+B,EAC/B;EAAES,WAAW;EAAEC;AAAwD,CAAC,EAC1D;EACd,MAAMa,8BAA8B,GAAGC,qBAAqB,CAACxB,MAAM,CAAC;EACpE,IAAIuB,8BAA8B,KAAK,IAAI,EAAE;IAC3C,OAAOb,OAAO;EAChB;EAEA,MAAMG,qBAAqB,GAAGC,eAAI,CAACC,OAAO,CAACN,WAAW,EAAEc,8BAA8B,CAAC;EACvFN,aAAE,CAACe,YAAY,CACbnB,qBAAqB,EACrBC,eAAI,CAACmB,IAAI,CAAC,IAAAC,sBAAa,EAACzB,WAAW,CAAC,EAAE,0BAA0B,CAClE,CAAC;EAED,MAAM0B,WAAW,GAAG,IAAAC,2BAAc,EAAC3B,WAAW,CAAC;EAC/C,MAAM4B,aAAa,GAAG,GAAGF,WAAW,2BAA2B;EAC/D,IAAI,CAACzB,OAAO,CAAC4B,OAAO,CAACD,aAAa,CAAC,EAAE;IACnC3B,OAAO,GAAG,IAAA6B,mCAAsB,EAAC;MAC/BC,QAAQ,EAAEH,aAAa;MACvBI,SAAS,EAAEN,WAAW;MACtBzB,OAAO;MACPgC,WAAW,EAAE,IAAI;MACjBC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;EACA,OAAOjC,OAAO;AAChB", "ignoreList": []}