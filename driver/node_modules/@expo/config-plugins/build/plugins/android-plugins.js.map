{"version": 3, "file": "android-plugins.js", "names": ["_withMod", "data", "require", "createAndroidManifestPlugin", "action", "name", "withUnknown", "config", "withAndroidManifest", "modResults", "Object", "defineProperty", "value", "createStringsXmlPlugin", "withStringsXml", "with<PERSON><PERSON>", "platform", "mod", "exports", "withAndroidColors", "withAndroidColorsNight", "withAndroidStyles", "withMainActivity", "withMainApplication", "withProjectBuildGradle", "withAppBuildGradle", "withSettingsGradle", "withGradleProperties"], "sources": ["../../src/plugins/android-plugins.ts"], "sourcesContent": ["import { withMod } from './withMod';\nimport { ConfigPlugin, ExportedConfigWithProps, Mod } from '../Plugin.types';\nimport { Manifest, Paths, Properties, Resources } from '../android';\n\ntype OptionalPromise<T> = T | Promise<T>;\n\ntype MutateDataAction<T> = (expo: ExportedConfigWithProps<T>, data: T) => OptionalPromise<T>;\n\n/**\n * Helper method for creating mods from existing config functions.\n *\n * @param action\n */\nexport function createAndroidManifestPlugin(\n  action: MutateDataAction<Manifest.AndroidManifest>,\n  name: string\n): ConfigPlugin {\n  const withUnknown: ConfigPlugin = (config) =>\n    withAndroidManifest(config, async (config) => {\n      config.modResults = await action(config, config.modResults);\n      return config;\n    });\n  if (name) {\n    Object.defineProperty(withUnknown, 'name', {\n      value: name,\n    });\n  }\n  return withUnknown;\n}\n\nexport function createStringsXmlPlugin(\n  action: MutateDataAction<Resources.ResourceXML>,\n  name: string\n): ConfigPlugin {\n  const withUnknown: ConfigPlugin = (config) =>\n    withStringsXml(config, async (config) => {\n      config.modResults = await action(config, config.modResults);\n      return config;\n    });\n  if (name) {\n    Object.defineProperty(withUnknown, 'name', {\n      value: name,\n    });\n  }\n  return withUnknown;\n}\n\n/**\n * Provides the AndroidManifest.xml for modification.\n *\n * @param config\n * @param action\n */\nexport const withAndroidManifest: ConfigPlugin<Mod<Manifest.AndroidManifest>> = (\n  config,\n  action\n) => {\n  return withMod(config, {\n    platform: 'android',\n    mod: 'manifest',\n    action,\n  });\n};\n\n/**\n * Provides the strings.xml for modification.\n *\n * @param config\n * @param action\n */\nexport const withStringsXml: ConfigPlugin<Mod<Resources.ResourceXML>> = (config, action) => {\n  return withMod(config, {\n    platform: 'android',\n    mod: 'strings',\n    action,\n  });\n};\n\n/**\n * Provides the `android/app/src/main/res/values/colors.xml` as JSON (parsed with [`xml2js`](https://www.npmjs.com/package/xml2js)).\n *\n * @param config\n * @param action\n */\nexport const withAndroidColors: ConfigPlugin<Mod<Resources.ResourceXML>> = (config, action) => {\n  return withMod(config, {\n    platform: 'android',\n    mod: 'colors',\n    action,\n  });\n};\n\n/**\n * Provides the `android/app/src/main/res/values-night/colors.xml` as JSON (parsed with [`xml2js`](https://www.npmjs.com/package/xml2js)).\n *\n * @param config\n * @param action\n */\nexport const withAndroidColorsNight: ConfigPlugin<Mod<Resources.ResourceXML>> = (\n  config,\n  action\n) => {\n  return withMod(config, {\n    platform: 'android',\n    mod: 'colorsNight',\n    action,\n  });\n};\n\n/**\n * Provides the `android/app/src/main/res/values/styles.xml` as JSON (parsed with [`xml2js`](https://www.npmjs.com/package/xml2js)).\n *\n * @param config\n * @param action\n */\nexport const withAndroidStyles: ConfigPlugin<Mod<Resources.ResourceXML>> = (config, action) => {\n  return withMod(config, {\n    platform: 'android',\n    mod: 'styles',\n    action,\n  });\n};\n\n/**\n * Provides the project MainActivity for modification.\n *\n * @param config\n * @param action\n */\nexport const withMainActivity: ConfigPlugin<Mod<Paths.ApplicationProjectFile>> = (\n  config,\n  action\n) => {\n  return withMod(config, {\n    platform: 'android',\n    mod: 'mainActivity',\n    action,\n  });\n};\n\n/**\n * Provides the project MainApplication for modification.\n *\n * @param config\n * @param action\n */\nexport const withMainApplication: ConfigPlugin<Mod<Paths.ApplicationProjectFile>> = (\n  config,\n  action\n) => {\n  return withMod(config, {\n    platform: 'android',\n    mod: 'mainApplication',\n    action,\n  });\n};\n\n/**\n * Provides the project /build.gradle for modification.\n *\n * @param config\n * @param action\n */\nexport const withProjectBuildGradle: ConfigPlugin<Mod<Paths.GradleProjectFile>> = (\n  config,\n  action\n) => {\n  return withMod(config, {\n    platform: 'android',\n    mod: 'projectBuildGradle',\n    action,\n  });\n};\n\n/**\n * Provides the app/build.gradle for modification.\n *\n * @param config\n * @param action\n */\nexport const withAppBuildGradle: ConfigPlugin<Mod<Paths.GradleProjectFile>> = (config, action) => {\n  return withMod(config, {\n    platform: 'android',\n    mod: 'appBuildGradle',\n    action,\n  });\n};\n\n/**\n * Provides the /settings.gradle for modification.\n *\n * @param config\n * @param action\n */\nexport const withSettingsGradle: ConfigPlugin<Mod<Paths.GradleProjectFile>> = (config, action) => {\n  return withMod(config, {\n    platform: 'android',\n    mod: 'settingsGradle',\n    action,\n  });\n};\n\n/**\n * Provides the /gradle.properties for modification.\n *\n * @param config\n * @param action\n */\nexport const withGradleProperties: ConfigPlugin<Mod<Properties.PropertiesItem[]>> = (\n  config,\n  action\n) => {\n  return withMod(config, {\n    platform: 'android',\n    mod: 'gradleProperties',\n    action,\n  });\n};\n"], "mappings": ";;;;;;;;AAAA,SAAAA,SAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,QAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAQA;AACA;AACA;AACA;AACA;AACO,SAASE,2BAA2BA,CACzCC,MAAkD,EAClDC,IAAY,EACE;EACd,MAAMC,WAAyB,GAAIC,MAAM,IACvCC,mBAAmB,CAACD,MAAM,EAAE,MAAOA,MAAM,IAAK;IAC5CA,MAAM,CAACE,UAAU,GAAG,MAAML,MAAM,CAACG,MAAM,EAAEA,MAAM,CAACE,UAAU,CAAC;IAC3D,OAAOF,MAAM;EACf,CAAC,CAAC;EACJ,IAAIF,IAAI,EAAE;IACRK,MAAM,CAACC,cAAc,CAACL,WAAW,EAAE,MAAM,EAAE;MACzCM,KAAK,EAAEP;IACT,CAAC,CAAC;EACJ;EACA,OAAOC,WAAW;AACpB;AAEO,SAASO,sBAAsBA,CACpCT,MAA+C,EAC/CC,IAAY,EACE;EACd,MAAMC,WAAyB,GAAIC,MAAM,IACvCO,cAAc,CAACP,MAAM,EAAE,MAAOA,MAAM,IAAK;IACvCA,MAAM,CAACE,UAAU,GAAG,MAAML,MAAM,CAACG,MAAM,EAAEA,MAAM,CAACE,UAAU,CAAC;IAC3D,OAAOF,MAAM;EACf,CAAC,CAAC;EACJ,IAAIF,IAAI,EAAE;IACRK,MAAM,CAACC,cAAc,CAACL,WAAW,EAAE,MAAM,EAAE;MACzCM,KAAK,EAAEP;IACT,CAAC,CAAC;EACJ;EACA,OAAOC,WAAW;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,MAAME,mBAAgE,GAAGA,CAC9ED,MAAM,EACNH,MAAM,KACH;EACH,OAAO,IAAAW,kBAAO,EAACR,MAAM,EAAE;IACrBS,QAAQ,EAAE,SAAS;IACnBC,GAAG,EAAE,UAAU;IACfb;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALAc,OAAA,CAAAV,mBAAA,GAAAA,mBAAA;AAMO,MAAMM,cAAwD,GAAGA,CAACP,MAAM,EAAEH,MAAM,KAAK;EAC1F,OAAO,IAAAW,kBAAO,EAACR,MAAM,EAAE;IACrBS,QAAQ,EAAE,SAAS;IACnBC,GAAG,EAAE,SAAS;IACdb;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALAc,OAAA,CAAAJ,cAAA,GAAAA,cAAA;AAMO,MAAMK,iBAA2D,GAAGA,CAACZ,MAAM,EAAEH,MAAM,KAAK;EAC7F,OAAO,IAAAW,kBAAO,EAACR,MAAM,EAAE;IACrBS,QAAQ,EAAE,SAAS;IACnBC,GAAG,EAAE,QAAQ;IACbb;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALAc,OAAA,CAAAC,iBAAA,GAAAA,iBAAA;AAMO,MAAMC,sBAAgE,GAAGA,CAC9Eb,MAAM,EACNH,MAAM,KACH;EACH,OAAO,IAAAW,kBAAO,EAACR,MAAM,EAAE;IACrBS,QAAQ,EAAE,SAAS;IACnBC,GAAG,EAAE,aAAa;IAClBb;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALAc,OAAA,CAAAE,sBAAA,GAAAA,sBAAA;AAMO,MAAMC,iBAA2D,GAAGA,CAACd,MAAM,EAAEH,MAAM,KAAK;EAC7F,OAAO,IAAAW,kBAAO,EAACR,MAAM,EAAE;IACrBS,QAAQ,EAAE,SAAS;IACnBC,GAAG,EAAE,QAAQ;IACbb;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALAc,OAAA,CAAAG,iBAAA,GAAAA,iBAAA;AAMO,MAAMC,gBAAiE,GAAGA,CAC/Ef,MAAM,EACNH,MAAM,KACH;EACH,OAAO,IAAAW,kBAAO,EAACR,MAAM,EAAE;IACrBS,QAAQ,EAAE,SAAS;IACnBC,GAAG,EAAE,cAAc;IACnBb;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALAc,OAAA,CAAAI,gBAAA,GAAAA,gBAAA;AAMO,MAAMC,mBAAoE,GAAGA,CAClFhB,MAAM,EACNH,MAAM,KACH;EACH,OAAO,IAAAW,kBAAO,EAACR,MAAM,EAAE;IACrBS,QAAQ,EAAE,SAAS;IACnBC,GAAG,EAAE,iBAAiB;IACtBb;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALAc,OAAA,CAAAK,mBAAA,GAAAA,mBAAA;AAMO,MAAMC,sBAAkE,GAAGA,CAChFjB,MAAM,EACNH,MAAM,KACH;EACH,OAAO,IAAAW,kBAAO,EAACR,MAAM,EAAE;IACrBS,QAAQ,EAAE,SAAS;IACnBC,GAAG,EAAE,oBAAoB;IACzBb;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALAc,OAAA,CAAAM,sBAAA,GAAAA,sBAAA;AAMO,MAAMC,kBAA8D,GAAGA,CAAClB,MAAM,EAAEH,MAAM,KAAK;EAChG,OAAO,IAAAW,kBAAO,EAACR,MAAM,EAAE;IACrBS,QAAQ,EAAE,SAAS;IACnBC,GAAG,EAAE,gBAAgB;IACrBb;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALAc,OAAA,CAAAO,kBAAA,GAAAA,kBAAA;AAMO,MAAMC,kBAA8D,GAAGA,CAACnB,MAAM,EAAEH,MAAM,KAAK;EAChG,OAAO,IAAAW,kBAAO,EAACR,MAAM,EAAE;IACrBS,QAAQ,EAAE,SAAS;IACnBC,GAAG,EAAE,gBAAgB;IACrBb;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALAc,OAAA,CAAAQ,kBAAA,GAAAA,kBAAA;AAMO,MAAMC,oBAAoE,GAAGA,CAClFpB,MAAM,EACNH,MAAM,KACH;EACH,OAAO,IAAAW,kBAAO,EAACR,MAAM,EAAE;IACrBS,QAAQ,EAAE,SAAS;IACnBC,GAAG,EAAE,kBAAkB;IACvBb;EACF,CAAC,CAAC;AACJ,CAAC;AAACc,OAAA,CAAAS,oBAAA,GAAAA,oBAAA", "ignoreList": []}