{"version": 3, "file": "ios-plugins.js", "names": ["_withMod", "data", "require", "_obj", "_warnings", "createInfoPlistPlugin", "action", "name", "withUnknown", "config", "withInfoPlist", "modResults", "Object", "defineProperty", "value", "createInfoPlistPluginWithPropertyGuard", "settings", "existingProperty", "expoPropertyGetter", "get", "expoConfigProperty", "modRawConfig", "ios", "infoPlist", "infoPlistProperty", "undefined", "addWarningIOS", "createEntitlementsPlugin", "withEntitlementsPlist", "withAppDelegate", "with<PERSON><PERSON>", "platform", "mod", "exports", "entitlements", "withExpoPlist", "withXcodeProject", "with<PERSON><PERSON><PERSON><PERSON>", "withPodfileProperties"], "sources": ["../../src/plugins/ios-plugins.ts"], "sourcesContent": ["import type { ExpoConfig } from '@expo/config-types';\nimport type { JSONObject } from '@expo/json-file';\nimport type { XcodeProject } from 'xcode';\n\nimport { withMod } from './withMod';\nimport type { ConfigPlugin, Mod } from '../Plugin.types';\nimport type { ExpoPlist, InfoPlist } from '../ios/IosConfig.types';\nimport type { AppDelegateProjectFile, PodfileProjectFile } from '../ios/Paths';\nimport { get } from '../utils/obj';\nimport { addWarningIOS } from '../utils/warnings';\n\ntype MutateInfoPlistAction = (\n  expo: ExpoConfig,\n  infoPlist: InfoPlist\n) => Promise<InfoPlist> | InfoPlist;\n\n/**\n * Helper method for creating mods from existing config functions.\n *\n * @param action\n */\nexport function createInfoPlistPlugin(action: MutateInfoPlistAction, name?: string): ConfigPlugin {\n  const withUnknown: ConfigPlugin = (config) =>\n    withInfoPlist(config, async (config) => {\n      config.modResults = await action(config, config.modResults);\n      return config;\n    });\n  if (name) {\n    Object.defineProperty(withUnknown, 'name', {\n      value: name,\n    });\n  }\n  return withUnknown;\n}\n\nexport function createInfoPlistPluginWithPropertyGuard(\n  action: MutateInfoPlistAction,\n  settings: {\n    infoPlistProperty: string;\n    expoConfigProperty: string;\n    expoPropertyGetter?: (config: ExpoConfig) => string;\n  },\n  name?: string\n): ConfigPlugin {\n  const withUnknown: ConfigPlugin = (config) =>\n    withInfoPlist(config, async (config) => {\n      const existingProperty = settings.expoPropertyGetter\n        ? settings.expoPropertyGetter(config)\n        : get(config, settings.expoConfigProperty);\n      // If the user explicitly sets a value in the infoPlist, we should respect that.\n      if (config.modRawConfig.ios?.infoPlist?.[settings.infoPlistProperty] === undefined) {\n        config.modResults = await action(config, config.modResults);\n      } else if (existingProperty !== undefined) {\n        // Only warn if there is a conflict.\n        addWarningIOS(\n          settings.expoConfigProperty,\n          `\"ios.infoPlist.${settings.infoPlistProperty}\" is set in the config. Ignoring abstract property \"${settings.expoConfigProperty}\": ${existingProperty}`\n        );\n      }\n\n      return config;\n    });\n  if (name) {\n    Object.defineProperty(withUnknown, 'name', {\n      value: name,\n    });\n  }\n  return withUnknown;\n}\n\ntype MutateEntitlementsPlistAction = (expo: ExpoConfig, entitlements: JSONObject) => JSONObject;\n\n/**\n * Helper method for creating mods from existing config functions.\n *\n * @param action\n */\nexport function createEntitlementsPlugin(\n  action: MutateEntitlementsPlistAction,\n  name: string\n): ConfigPlugin {\n  const withUnknown: ConfigPlugin = (config) =>\n    withEntitlementsPlist(config, async (config) => {\n      config.modResults = await action(config, config.modResults);\n      return config;\n    });\n  if (name) {\n    Object.defineProperty(withUnknown, 'name', {\n      value: name,\n    });\n  }\n  return withUnknown;\n}\n\n/**\n * Provides the AppDelegate file for modification.\n *\n * @param config\n * @param action\n */\nexport const withAppDelegate: ConfigPlugin<Mod<AppDelegateProjectFile>> = (config, action) => {\n  return withMod(config, {\n    platform: 'ios',\n    mod: 'appDelegate',\n    action,\n  });\n};\n\n/**\n * Provides the Info.plist file for modification.\n * Keeps the config's expo.ios.infoPlist object in sync with the data.\n *\n * @param config\n * @param action\n */\nexport const withInfoPlist: ConfigPlugin<Mod<InfoPlist>> = (config, action) => {\n  return withMod<InfoPlist>(config, {\n    platform: 'ios',\n    mod: 'infoPlist',\n    async action(config) {\n      config = await action(config);\n      if (!config.ios) {\n        config.ios = {};\n      }\n      config.ios.infoPlist = config.modResults;\n      return config;\n    },\n  });\n};\n\n/**\n * Provides the main .entitlements file for modification.\n * Keeps the config's expo.ios.entitlements object in sync with the data.\n *\n * @param config\n * @param action\n */\nexport const withEntitlementsPlist: ConfigPlugin<Mod<JSONObject>> = (config, action) => {\n  return withMod<JSONObject>(config, {\n    platform: 'ios',\n    mod: 'entitlements',\n    async action(config) {\n      config = await action(config);\n      if (!config.ios) {\n        config.ios = {};\n      }\n      config.ios.entitlements = config.modResults;\n      return config;\n    },\n  });\n};\n\n/**\n * Provides the Expo.plist for modification.\n *\n * @param config\n * @param action\n */\nexport const withExpoPlist: ConfigPlugin<Mod<ExpoPlist>> = (config, action) => {\n  return withMod(config, {\n    platform: 'ios',\n    mod: 'expoPlist',\n    action,\n  });\n};\n\n/**\n * Provides the main .xcodeproj for modification.\n *\n * @param config\n * @param action\n */\nexport const withXcodeProject: ConfigPlugin<Mod<XcodeProject>> = (config, action) => {\n  return withMod(config, {\n    platform: 'ios',\n    mod: 'xcodeproj',\n    action,\n  });\n};\n\n/**\n * Provides the Podfile for modification.\n *\n * @param config\n * @param action\n */\nexport const withPodfile: ConfigPlugin<Mod<PodfileProjectFile>> = (config, action) => {\n  return withMod(config, {\n    platform: 'ios',\n    mod: 'podfile',\n    action,\n  });\n};\n\n/**\n * Provides the Podfile.properties.json for modification.\n *\n * @param config\n * @param action\n */\nexport const withPodfileProperties: ConfigPlugin<Mod<Record<string, string>>> = (\n  config,\n  action\n) => {\n  return withMod(config, {\n    platform: 'ios',\n    mod: 'podfileProperties',\n    action,\n  });\n};\n"], "mappings": ";;;;;;;;;AAIA,SAAAA,SAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,QAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAIA,SAAAE,KAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,IAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,UAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,SAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAOA;AACA;AACA;AACA;AACA;AACO,SAASI,qBAAqBA,CAACC,MAA6B,EAAEC,IAAa,EAAgB;EAChG,MAAMC,WAAyB,GAAIC,MAAM,IACvCC,aAAa,CAACD,MAAM,EAAE,MAAOA,MAAM,IAAK;IACtCA,MAAM,CAACE,UAAU,GAAG,MAAML,MAAM,CAACG,MAAM,EAAEA,MAAM,CAACE,UAAU,CAAC;IAC3D,OAAOF,MAAM;EACf,CAAC,CAAC;EACJ,IAAIF,IAAI,EAAE;IACRK,MAAM,CAACC,cAAc,CAACL,WAAW,EAAE,MAAM,EAAE;MACzCM,KAAK,EAAEP;IACT,CAAC,CAAC;EACJ;EACA,OAAOC,WAAW;AACpB;AAEO,SAASO,sCAAsCA,CACpDT,MAA6B,EAC7BU,QAIC,EACDT,IAAa,EACC;EACd,MAAMC,WAAyB,GAAIC,MAAM,IACvCC,aAAa,CAACD,MAAM,EAAE,MAAOA,MAAM,IAAK;IACtC,MAAMQ,gBAAgB,GAAGD,QAAQ,CAACE,kBAAkB,GAChDF,QAAQ,CAACE,kBAAkB,CAACT,MAAM,CAAC,GACnC,IAAAU,UAAG,EAACV,MAAM,EAAEO,QAAQ,CAACI,kBAAkB,CAAC;IAC5C;IACA,IAAIX,MAAM,CAACY,YAAY,CAACC,GAAG,EAAEC,SAAS,GAAGP,QAAQ,CAACQ,iBAAiB,CAAC,KAAKC,SAAS,EAAE;MAClFhB,MAAM,CAACE,UAAU,GAAG,MAAML,MAAM,CAACG,MAAM,EAAEA,MAAM,CAACE,UAAU,CAAC;IAC7D,CAAC,MAAM,IAAIM,gBAAgB,KAAKQ,SAAS,EAAE;MACzC;MACA,IAAAC,yBAAa,EACXV,QAAQ,CAACI,kBAAkB,EAC3B,kBAAkBJ,QAAQ,CAACQ,iBAAiB,uDAAuDR,QAAQ,CAACI,kBAAkB,MAAMH,gBAAgB,EACtJ,CAAC;IACH;IAEA,OAAOR,MAAM;EACf,CAAC,CAAC;EACJ,IAAIF,IAAI,EAAE;IACRK,MAAM,CAACC,cAAc,CAACL,WAAW,EAAE,MAAM,EAAE;MACzCM,KAAK,EAAEP;IACT,CAAC,CAAC;EACJ;EACA,OAAOC,WAAW;AACpB;AAIA;AACA;AACA;AACA;AACA;AACO,SAASmB,wBAAwBA,CACtCrB,MAAqC,EACrCC,IAAY,EACE;EACd,MAAMC,WAAyB,GAAIC,MAAM,IACvCmB,qBAAqB,CAACnB,MAAM,EAAE,MAAOA,MAAM,IAAK;IAC9CA,MAAM,CAACE,UAAU,GAAG,MAAML,MAAM,CAACG,MAAM,EAAEA,MAAM,CAACE,UAAU,CAAC;IAC3D,OAAOF,MAAM;EACf,CAAC,CAAC;EACJ,IAAIF,IAAI,EAAE;IACRK,MAAM,CAACC,cAAc,CAACL,WAAW,EAAE,MAAM,EAAE;MACzCM,KAAK,EAAEP;IACT,CAAC,CAAC;EACJ;EACA,OAAOC,WAAW;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMqB,eAA0D,GAAGA,CAACpB,MAAM,EAAEH,MAAM,KAAK;EAC5F,OAAO,IAAAwB,kBAAO,EAACrB,MAAM,EAAE;IACrBsB,QAAQ,EAAE,KAAK;IACfC,GAAG,EAAE,aAAa;IAClB1B;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AANA2B,OAAA,CAAAJ,eAAA,GAAAA,eAAA;AAOO,MAAMnB,aAA2C,GAAGA,CAACD,MAAM,EAAEH,MAAM,KAAK;EAC7E,OAAO,IAAAwB,kBAAO,EAAYrB,MAAM,EAAE;IAChCsB,QAAQ,EAAE,KAAK;IACfC,GAAG,EAAE,WAAW;IAChB,MAAM1B,MAAMA,CAACG,MAAM,EAAE;MACnBA,MAAM,GAAG,MAAMH,MAAM,CAACG,MAAM,CAAC;MAC7B,IAAI,CAACA,MAAM,CAACa,GAAG,EAAE;QACfb,MAAM,CAACa,GAAG,GAAG,CAAC,CAAC;MACjB;MACAb,MAAM,CAACa,GAAG,CAACC,SAAS,GAAGd,MAAM,CAACE,UAAU;MACxC,OAAOF,MAAM;IACf;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AANAwB,OAAA,CAAAvB,aAAA,GAAAA,aAAA;AAOO,MAAMkB,qBAAoD,GAAGA,CAACnB,MAAM,EAAEH,MAAM,KAAK;EACtF,OAAO,IAAAwB,kBAAO,EAAarB,MAAM,EAAE;IACjCsB,QAAQ,EAAE,KAAK;IACfC,GAAG,EAAE,cAAc;IACnB,MAAM1B,MAAMA,CAACG,MAAM,EAAE;MACnBA,MAAM,GAAG,MAAMH,MAAM,CAACG,MAAM,CAAC;MAC7B,IAAI,CAACA,MAAM,CAACa,GAAG,EAAE;QACfb,MAAM,CAACa,GAAG,GAAG,CAAC,CAAC;MACjB;MACAb,MAAM,CAACa,GAAG,CAACY,YAAY,GAAGzB,MAAM,CAACE,UAAU;MAC3C,OAAOF,MAAM;IACf;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALAwB,OAAA,CAAAL,qBAAA,GAAAA,qBAAA;AAMO,MAAMO,aAA2C,GAAGA,CAAC1B,MAAM,EAAEH,MAAM,KAAK;EAC7E,OAAO,IAAAwB,kBAAO,EAACrB,MAAM,EAAE;IACrBsB,QAAQ,EAAE,KAAK;IACfC,GAAG,EAAE,WAAW;IAChB1B;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALA2B,OAAA,CAAAE,aAAA,GAAAA,aAAA;AAMO,MAAMC,gBAAiD,GAAGA,CAAC3B,MAAM,EAAEH,MAAM,KAAK;EACnF,OAAO,IAAAwB,kBAAO,EAACrB,MAAM,EAAE;IACrBsB,QAAQ,EAAE,KAAK;IACfC,GAAG,EAAE,WAAW;IAChB1B;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALA2B,OAAA,CAAAG,gBAAA,GAAAA,gBAAA;AAMO,MAAMC,WAAkD,GAAGA,CAAC5B,MAAM,EAAEH,MAAM,KAAK;EACpF,OAAO,IAAAwB,kBAAO,EAACrB,MAAM,EAAE;IACrBsB,QAAQ,EAAE,KAAK;IACfC,GAAG,EAAE,SAAS;IACd1B;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALA2B,OAAA,CAAAI,WAAA,GAAAA,WAAA;AAMO,MAAMC,qBAAgE,GAAGA,CAC9E7B,MAAM,EACNH,MAAM,KACH;EACH,OAAO,IAAAwB,kBAAO,EAACrB,MAAM,EAAE;IACrBsB,QAAQ,EAAE,KAAK;IACfC,GAAG,EAAE,mBAAmB;IACxB1B;EACF,CAAC,CAAC;AACJ,CAAC;AAAC2B,OAAA,CAAAK,qBAAA,GAAAA,qBAAA", "ignoreList": []}