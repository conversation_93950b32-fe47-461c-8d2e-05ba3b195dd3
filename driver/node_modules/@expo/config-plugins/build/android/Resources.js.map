{"version": 3, "file": "Resources.js", "names": ["_XML", "data", "require", "fallbackResourceString", "readResourcesXMLAsync", "path", "fallback", "xml", "readXMLAsync", "resources", "ensureDefaultResourceXML", "buildResourceItem", "name", "value", "targetApi", "translatable", "item", "$", "_", "undefined", "String", "buildResourceGroup", "parent", "items", "findResourceGroup", "group", "app", "filter", "head", "matches", "getResourceItemsAsObject", "reduce", "prev", "curr", "getObjectAsResourceItems", "obj", "Object", "entries", "map", "getObjectAsResourceGroup"], "sources": ["../../src/android/Resources.ts"], "sourcesContent": ["import { readXMLAsync, XMLObject } from '../utils/XML';\n\nexport type ResourceGroupXML = {\n  $: {\n    name: string;\n    parent: string;\n  };\n  item: ResourceItemXML[];\n};\n\nexport type ResourceXML = {\n  resources: {\n    $?: {\n      'xmlns:tools'?: string;\n    };\n    color?: ResourceItemXML[];\n    string?: ResourceItemXML[];\n    style?: ResourceGroupXML[];\n    // Add more if needed...\n  };\n};\n\nexport type ResourceItemXML = {\n  _: string;\n  $: {\n    name: string;\n    'tools:targetApi'?: string;\n    translatable?: string;\n  };\n};\n/**\n * Name of the resource folder.\n */\nexport type ResourceKind =\n  | 'values'\n  | 'values-night'\n  | 'values-v23'\n  | 'values-night-v23'\n  | 'drawable';\n\nconst fallbackResourceString = `<?xml version=\"1.0\" encoding=\"utf-8\"?><resources></resources>`;\n\n/**\n * Read an XML file while providing a default fallback for resource files.\n *\n * @param options path to the XML file, returns a fallback XML if the path doesn't exist.\n */\nexport async function readResourcesXMLAsync({\n  path,\n  fallback = fallbackResourceString,\n}: {\n  path: string;\n  fallback?: string | null;\n}): Promise<ResourceXML> {\n  const xml = await readXMLAsync({ path, fallback });\n  // Ensure the type is expected.\n  if (!xml.resources) {\n    xml.resources = {};\n  }\n  return xml as ResourceXML;\n}\n\n/**\n * Ensure the provided xml has a `resources` object (the expected shape).\n *\n * @param xml\n */\nexport function ensureDefaultResourceXML(xml: XMLObject): ResourceXML {\n  if (!xml) {\n    xml = { resources: {} };\n  }\n  if (!xml.resources) {\n    xml.resources = {};\n  }\n\n  return xml as ResourceXML;\n}\n\n/**\n * Build a `ResourceItemXML` given its `name` and `value`. This makes things a bit more readable.\n *\n * - JSON: `{ $: { name }, _: value }`\n * - XML: `<item name=\"NAME\">VALUE</item>`\n *\n * @param props name and value strings.\n */\nexport function buildResourceItem({\n  name,\n  value,\n  targetApi,\n  translatable,\n}: {\n  name: string;\n  value: string;\n  targetApi?: string;\n  translatable?: boolean;\n}): ResourceItemXML {\n  const item: ResourceItemXML = { $: { name }, _: value };\n  if (targetApi) {\n    item.$['tools:targetApi'] = targetApi;\n  }\n  if (translatable !== undefined) {\n    item.$['translatable'] = String(translatable);\n  }\n  return item;\n}\n\nexport function buildResourceGroup(parent: {\n  name: string;\n  parent: string;\n  items?: ResourceItemXML[];\n}): ResourceGroupXML {\n  return {\n    $: { name: parent.name, parent: parent.parent },\n    item: parent.items ?? [],\n  };\n}\n\nexport function findResourceGroup(\n  xml: ResourceGroupXML[] | undefined,\n  group: { name: string; parent?: string }\n): ResourceGroupXML | null {\n  const app = xml?.filter?.(({ $: head }) => {\n    let matches = head.name === group.name;\n    if (group.parent != null && matches) {\n      matches = head.parent === group.parent;\n    }\n    return matches;\n  })?.[0];\n  return app ?? null;\n}\n\n/**\n * Helper to convert a basic XML object into a simple k/v pair.\n *\n * @param xml\n * @returns\n */\nexport function getResourceItemsAsObject(xml: ResourceItemXML[]): Record<string, string> | null {\n  return xml.reduce(\n    (prev, curr) => ({\n      ...prev,\n      [curr.$.name]: curr._,\n    }),\n    {}\n  );\n}\n\n/**\n * Helper to convert a basic k/v object to a ResourceItemXML array.\n *\n * @param xml\n * @returns\n */\nexport function getObjectAsResourceItems(obj: Record<string, string>): ResourceItemXML[] {\n  return Object.entries(obj).map(([name, value]) => ({\n    $: { name },\n    _: value,\n  }));\n}\n\nexport function getObjectAsResourceGroup(group: {\n  name: string;\n  parent: string;\n  item: Record<string, string>;\n}): ResourceGroupXML {\n  return {\n    $: {\n      name: group.name,\n      parent: group.parent,\n    },\n    item: getObjectAsResourceItems(group.item),\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;AAAA,SAAAA,KAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,IAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AA8BA;AACA;AACA;;AAQA,MAAME,sBAAsB,GAAG,+DAA+D;;AAE9F;AACA;AACA;AACA;AACA;AACO,eAAeC,qBAAqBA,CAAC;EAC1CC,IAAI;EACJC,QAAQ,GAAGH;AAIb,CAAC,EAAwB;EACvB,MAAMI,GAAG,GAAG,MAAM,IAAAC,mBAAY,EAAC;IAAEH,IAAI;IAAEC;EAAS,CAAC,CAAC;EAClD;EACA,IAAI,CAACC,GAAG,CAACE,SAAS,EAAE;IAClBF,GAAG,CAACE,SAAS,GAAG,CAAC,CAAC;EACpB;EACA,OAAOF,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASG,wBAAwBA,CAACH,GAAc,EAAe;EACpE,IAAI,CAACA,GAAG,EAAE;IACRA,GAAG,GAAG;MAAEE,SAAS,EAAE,CAAC;IAAE,CAAC;EACzB;EACA,IAAI,CAACF,GAAG,CAACE,SAAS,EAAE;IAClBF,GAAG,CAACE,SAAS,GAAG,CAAC,CAAC;EACpB;EAEA,OAAOF,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASI,iBAAiBA,CAAC;EAChCC,IAAI;EACJC,KAAK;EACLC,SAAS;EACTC;AAMF,CAAC,EAAmB;EAClB,MAAMC,IAAqB,GAAG;IAAEC,CAAC,EAAE;MAAEL;IAAK,CAAC;IAAEM,CAAC,EAAEL;EAAM,CAAC;EACvD,IAAIC,SAAS,EAAE;IACbE,IAAI,CAACC,CAAC,CAAC,iBAAiB,CAAC,GAAGH,SAAS;EACvC;EACA,IAAIC,YAAY,KAAKI,SAAS,EAAE;IAC9BH,IAAI,CAACC,CAAC,CAAC,cAAc,CAAC,GAAGG,MAAM,CAACL,YAAY,CAAC;EAC/C;EACA,OAAOC,IAAI;AACb;AAEO,SAASK,kBAAkBA,CAACC,MAIlC,EAAoB;EACnB,OAAO;IACLL,CAAC,EAAE;MAAEL,IAAI,EAAEU,MAAM,CAACV,IAAI;MAAEU,MAAM,EAAEA,MAAM,CAACA;IAAO,CAAC;IAC/CN,IAAI,EAAEM,MAAM,CAACC,KAAK,IAAI;EACxB,CAAC;AACH;AAEO,SAASC,iBAAiBA,CAC/BjB,GAAmC,EACnCkB,KAAwC,EACf;EACzB,MAAMC,GAAG,GAAGnB,GAAG,EAAEoB,MAAM,GAAG,CAAC;IAAEV,CAAC,EAAEW;EAAK,CAAC,KAAK;IACzC,IAAIC,OAAO,GAAGD,IAAI,CAAChB,IAAI,KAAKa,KAAK,CAACb,IAAI;IACtC,IAAIa,KAAK,CAACH,MAAM,IAAI,IAAI,IAAIO,OAAO,EAAE;MACnCA,OAAO,GAAGD,IAAI,CAACN,MAAM,KAAKG,KAAK,CAACH,MAAM;IACxC;IACA,OAAOO,OAAO;EAChB,CAAC,CAAC,GAAG,CAAC,CAAC;EACP,OAAOH,GAAG,IAAI,IAAI;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASI,wBAAwBA,CAACvB,GAAsB,EAAiC;EAC9F,OAAOA,GAAG,CAACwB,MAAM,CACf,CAACC,IAAI,EAAEC,IAAI,MAAM;IACf,GAAGD,IAAI;IACP,CAACC,IAAI,CAAChB,CAAC,CAACL,IAAI,GAAGqB,IAAI,CAACf;EACtB,CAAC,CAAC,EACF,CAAC,CACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASgB,wBAAwBA,CAACC,GAA2B,EAAqB;EACvF,OAAOC,MAAM,CAACC,OAAO,CAACF,GAAG,CAAC,CAACG,GAAG,CAAC,CAAC,CAAC1B,IAAI,EAAEC,KAAK,CAAC,MAAM;IACjDI,CAAC,EAAE;MAAEL;IAAK,CAAC;IACXM,CAAC,EAAEL;EACL,CAAC,CAAC,CAAC;AACL;AAEO,SAAS0B,wBAAwBA,CAACd,KAIxC,EAAoB;EACnB,OAAO;IACLR,CAAC,EAAE;MACDL,IAAI,EAAEa,KAAK,CAACb,IAAI;MAChBU,MAAM,EAAEG,KAAK,CAACH;IAChB,CAAC;IACDN,IAAI,EAAEkB,wBAAwB,CAACT,KAAK,CAACT,IAAI;EAC3C,CAAC;AACH", "ignoreList": []}