{"version": 3, "sources": ["../../../../../../src/start/server/metro/dev-server/createMessageSocket.ts"], "sourcesContent": ["import { parse } from 'node:url';\nimport { type WebSocket, WebSocketServer, type RawData as WebSocketRawData } from 'ws';\n\nimport { createBroadcaster } from './utils/createSocketBroadcaster';\nimport { createSocketMap, type SocketId } from './utils/createSocketMap';\nimport { parseRawMessage, serializeMessage } from './utils/socketMessages';\n\ntype MessageSocketOptions = {\n  logger: {\n    warn: (message: string) => any;\n  };\n};\n\n/**\n * Client \"command\" server that dispatches basic commands to connected clients.\n * This basic client to client communication, reload, or open dev menu cli commands.\n */\nexport function createMessagesSocket(options: MessageSocketOptions) {\n  const clients = createSocketMap();\n  const broadcast = createBroadcaster(clients.map);\n\n  const server = new WebSocketServer({ noServer: true });\n\n  server.on('connection', (socket, req) => {\n    const client = clients.registerSocket(socket);\n\n    // Assign the query parameters to the socket, used for `getpeers` requests\n    // NOTE(cedric): this looks like a legacy feature, might be able to drop it\n    if (req.url) {\n      Object.defineProperty(socket, '_upgradeQuery', {\n        value: parse(req.url).query,\n      });\n    }\n\n    // Register disconnect handlers\n    socket.on('close', client.terminate);\n    socket.on('error', client.terminate);\n    // Register message handler\n    socket.on('message', createClientMessageHandler(socket, client.id, clients, broadcast));\n  });\n\n  return {\n    endpoint: '/message' as const,\n    server,\n    broadcast: (method: BroadcastMessage['method'], params?: BroadcastMessage['params']) => {\n      if (clients.map.size === 0) {\n        return options.logger.warn(\n          `No apps connected. Sending \"${method}\" to all React Native apps failed. Make sure your app is running in the simulator or on a phone connected via USB.`\n        );\n      }\n\n      broadcast(null, serializeMessage({ method, params }));\n    },\n  };\n}\n\nfunction createClientMessageHandler(\n  socket: WebSocket,\n  clientId: SocketId,\n  clients: ReturnType<typeof createSocketMap>,\n  broadcast: ReturnType<typeof createBroadcaster>\n) {\n  function handleServerRequest(message: RequestMessage) {\n    // Ignore messages without identifiers, unable to link responses\n    if (!message.id) return;\n\n    if (message.method === 'getid') {\n      return socket.send(serializeMessage({ id: message.id, result: clientId }));\n    }\n\n    if (message.method === 'getpeers') {\n      const peers: Record<string, any> = {};\n      clients.map.forEach((peerSocket, peerSocketId) => {\n        if (peerSocketId !== clientId) {\n          peers[peerSocketId] = '_upgradeQuery' in peerSocket ? peerSocket._upgradeQuery : {};\n        }\n      });\n      return socket.send(serializeMessage({ id: message.id, result: peers }));\n    }\n  }\n\n  return (data: WebSocketRawData, isBinary: boolean) => {\n    const message = parseRawMessage<IncomingMessage>(data, isBinary);\n    if (!message) return;\n\n    // Handle broadcast messages\n    if (messageIsBroadcast(message)) {\n      return broadcast(null, data.toString());\n    }\n\n    // Handle incoming requests from clients\n    if (messageIsRequest(message)) {\n      if (message.target === 'server') {\n        return handleServerRequest(message);\n      }\n\n      return clients.findSocket(message.target)?.send(\n        serializeMessage({\n          method: message.method,\n          params: message.params,\n          id: !message.id\n            ? undefined\n            : {\n                requestId: message.id,\n                clientId,\n              },\n        })\n      );\n    }\n\n    // Handle incoming responses\n    if (messageIsResponse(message)) {\n      return clients.findSocket(message.id.clientId)?.send(\n        serializeMessage({\n          id: message.id.requestId,\n          result: message.result,\n          error: message.error,\n        })\n      );\n    }\n  };\n}\n\ntype MessageId = {\n  requestId: string;\n  clientId: SocketId;\n};\n\ntype IncomingMessage = BroadcastMessage | RequestMessage | ResponseMessage;\n\ntype BroadcastMessage = {\n  method: string;\n  params?: Record<string, any>;\n};\n\ntype RequestMessage = {\n  method: string;\n  params?: Record<string, any>;\n  target: string;\n  id?: string;\n};\n\ntype ResponseMessage = {\n  result?: any;\n  error?: Error;\n  id: MessageId;\n};\n\nfunction messageIsBroadcast(message: IncomingMessage): message is BroadcastMessage {\n  return (\n    'method' in message &&\n    typeof message.method === 'string' &&\n    (!('id' in message) || message.id === undefined) &&\n    (!('target' in message) || message.target === undefined)\n  );\n}\n\nfunction messageIsRequest(message: IncomingMessage): message is RequestMessage {\n  return (\n    'method' in message &&\n    typeof message.method === 'string' &&\n    'target' in message &&\n    typeof message.target === 'string'\n  );\n}\n\nfunction messageIsResponse(message: IncomingMessage): message is ResponseMessage {\n  return (\n    'id' in message &&\n    typeof message.id === 'object' &&\n    typeof message.id.requestId !== 'undefined' &&\n    typeof message.id.clientId === 'string' &&\n    (('result' in message && !!message.result) || ('error' in message && !!message.error))\n  );\n}\n"], "names": ["createMessagesSocket", "options", "clients", "createSocketMap", "broadcast", "createBroadcaster", "map", "server", "WebSocketServer", "noServer", "on", "socket", "req", "client", "registerSocket", "url", "Object", "defineProperty", "value", "parse", "query", "terminate", "createClientMessageHandler", "id", "endpoint", "method", "params", "size", "logger", "warn", "serializeMessage", "clientId", "handleServerRequest", "message", "send", "result", "peers", "for<PERSON>ach", "peerSocket", "peerSocketId", "_upgradeQuery", "data", "isBinary", "parseRawMessage", "messageIsBroadcast", "toString", "messageIsRequest", "target", "findSocket", "undefined", "requestId", "messageIsResponse", "error"], "mappings": ";;;;+BAiBgBA;;;eAAAA;;;;yBAjBM;;;;;;;yBAC4D;;;;;;yCAEhD;iCACa;gCACG;AAY3C,SAASA,qBAAqBC,OAA6B;IAChE,MAAMC,UAAUC,IAAAA,gCAAe;IAC/B,MAAMC,YAAYC,IAAAA,0CAAiB,EAACH,QAAQI,GAAG;IAE/C,MAAMC,SAAS,IAAIC,CAAAA,KAAc,iBAAC,CAAC;QAAEC,UAAU;IAAK;IAEpDF,OAAOG,EAAE,CAAC,cAAc,CAACC,QAAQC;QAC/B,MAAMC,SAASX,QAAQY,cAAc,CAACH;QAEtC,0EAA0E;QAC1E,2EAA2E;QAC3E,IAAIC,IAAIG,GAAG,EAAE;YACXC,OAAOC,cAAc,CAACN,QAAQ,iBAAiB;gBAC7CO,OAAOC,IAAAA,gBAAK,EAACP,IAAIG,GAAG,EAAEK,KAAK;YAC7B;QACF;QAEA,+BAA+B;QAC/BT,OAAOD,EAAE,CAAC,SAASG,OAAOQ,SAAS;QACnCV,OAAOD,EAAE,CAAC,SAASG,OAAOQ,SAAS;QACnC,2BAA2B;QAC3BV,OAAOD,EAAE,CAAC,WAAWY,2BAA2BX,QAAQE,OAAOU,EAAE,EAAErB,SAASE;IAC9E;IAEA,OAAO;QACLoB,UAAU;QACVjB;QACAH,WAAW,CAACqB,QAAoCC;YAC9C,IAAIxB,QAAQI,GAAG,CAACqB,IAAI,KAAK,GAAG;gBAC1B,OAAO1B,QAAQ2B,MAAM,CAACC,IAAI,CACxB,CAAC,4BAA4B,EAAEJ,OAAO,kHAAkH,CAAC;YAE7J;YAEArB,UAAU,MAAM0B,IAAAA,gCAAgB,EAAC;gBAAEL;gBAAQC;YAAO;QACpD;IACF;AACF;AAEA,SAASJ,2BACPX,MAAiB,EACjBoB,QAAkB,EAClB7B,OAA2C,EAC3CE,SAA+C;IAE/C,SAAS4B,oBAAoBC,OAAuB;QAClD,gEAAgE;QAChE,IAAI,CAACA,QAAQV,EAAE,EAAE;QAEjB,IAAIU,QAAQR,MAAM,KAAK,SAAS;YAC9B,OAAOd,OAAOuB,IAAI,CAACJ,IAAAA,gCAAgB,EAAC;gBAAEP,IAAIU,QAAQV,EAAE;gBAAEY,QAAQJ;YAAS;QACzE;QAEA,IAAIE,QAAQR,MAAM,KAAK,YAAY;YACjC,MAAMW,QAA6B,CAAC;YACpClC,QAAQI,GAAG,CAAC+B,OAAO,CAAC,CAACC,YAAYC;gBAC/B,IAAIA,iBAAiBR,UAAU;oBAC7BK,KAAK,CAACG,aAAa,GAAG,mBAAmBD,aAAaA,WAAWE,aAAa,GAAG,CAAC;gBACpF;YACF;YACA,OAAO7B,OAAOuB,IAAI,CAACJ,IAAAA,gCAAgB,EAAC;gBAAEP,IAAIU,QAAQV,EAAE;gBAAEY,QAAQC;YAAM;QACtE;IACF;IAEA,OAAO,CAACK,MAAwBC;QAC9B,MAAMT,UAAUU,IAAAA,+BAAe,EAAkBF,MAAMC;QACvD,IAAI,CAACT,SAAS;QAEd,4BAA4B;QAC5B,IAAIW,mBAAmBX,UAAU;YAC/B,OAAO7B,UAAU,MAAMqC,KAAKI,QAAQ;QACtC;QAEA,wCAAwC;QACxC,IAAIC,iBAAiBb,UAAU;gBAKtB/B;YAJP,IAAI+B,QAAQc,MAAM,KAAK,UAAU;gBAC/B,OAAOf,oBAAoBC;YAC7B;YAEA,QAAO/B,sBAAAA,QAAQ8C,UAAU,CAACf,QAAQc,MAAM,sBAAjC7C,oBAAoCgC,IAAI,CAC7CJ,IAAAA,gCAAgB,EAAC;gBACfL,QAAQQ,QAAQR,MAAM;gBACtBC,QAAQO,QAAQP,MAAM;gBACtBH,IAAI,CAACU,QAAQV,EAAE,GACX0B,YACA;oBACEC,WAAWjB,QAAQV,EAAE;oBACrBQ;gBACF;YACN;QAEJ;QAEA,4BAA4B;QAC5B,IAAIoB,kBAAkBlB,UAAU;gBACvB/B;YAAP,QAAOA,uBAAAA,QAAQ8C,UAAU,CAACf,QAAQV,EAAE,CAACQ,QAAQ,sBAAtC7B,qBAAyCgC,IAAI,CAClDJ,IAAAA,gCAAgB,EAAC;gBACfP,IAAIU,QAAQV,EAAE,CAAC2B,SAAS;gBACxBf,QAAQF,QAAQE,MAAM;gBACtBiB,OAAOnB,QAAQmB,KAAK;YACtB;QAEJ;IACF;AACF;AA2BA,SAASR,mBAAmBX,OAAwB;IAClD,OACE,YAAYA,WACZ,OAAOA,QAAQR,MAAM,KAAK,YACzB,CAAA,CAAE,CAAA,QAAQQ,OAAM,KAAMA,QAAQV,EAAE,KAAK0B,SAAQ,KAC7C,CAAA,CAAE,CAAA,YAAYhB,OAAM,KAAMA,QAAQc,MAAM,KAAKE,SAAQ;AAE1D;AAEA,SAASH,iBAAiBb,OAAwB;IAChD,OACE,YAAYA,WACZ,OAAOA,QAAQR,MAAM,KAAK,YAC1B,YAAYQ,WACZ,OAAOA,QAAQc,MAAM,KAAK;AAE9B;AAEA,SAASI,kBAAkBlB,OAAwB;IACjD,OACE,QAAQA,WACR,OAAOA,QAAQV,EAAE,KAAK,YACtB,OAAOU,QAAQV,EAAE,CAAC2B,SAAS,KAAK,eAChC,OAAOjB,QAAQV,EAAE,CAACQ,QAAQ,KAAK,YAC9B,CAAA,AAAC,YAAYE,WAAW,CAAC,CAACA,QAAQE,MAAM,IAAM,WAAWF,WAAW,CAAC,CAACA,QAAQmB,KAAK;AAExF"}