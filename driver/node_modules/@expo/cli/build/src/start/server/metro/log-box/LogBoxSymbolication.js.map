{"version": 3, "sources": ["../../../../../../src/start/server/metro/log-box/LogBoxSymbolication.ts"], "sourcesContent": ["/**\n * Copyright (c) 650 Industries.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { parse, StackFrame as UpstreamStackFrame } from 'stacktrace-parser';\n\nexport type CodeFrame = {\n  content: string;\n  location?: {\n    row: number;\n    column: number;\n    [key: string]: any;\n  } | null;\n  fileName: string;\n\n  // TODO: When React switched to using call stack frames,\n  // we gained the ability to use the collapse flag, but\n  // it is not integrated into the LogBox UI.\n  collapse?: boolean;\n};\n\nexport type SymbolicatedStackTrace = {\n  stack: UpstreamStackFrame[];\n  codeFrame?: CodeFrame;\n};\n\nexport type StackFrame = UpstreamStackFrame & { collapse?: boolean };\n\nconst cache: Map<StackFrame[], Promise<SymbolicatedStackTrace>> = new Map();\n\n/**\n * Sanitize because sometimes `symbolicateStackTrace` gives us invalid values.\n */\nconst sanitize = ({\n  stack: maybeStack,\n  codeFrame,\n}: SymbolicatedStackTrace): SymbolicatedStackTrace => {\n  if (!Array.isArray(maybeStack)) {\n    throw new Error('Expected stack to be an array.');\n  }\n  const stack: StackFrame[] = [];\n  for (const maybeFrame of maybeStack) {\n    let collapse = false;\n    if ('collapse' in maybeFrame) {\n      if (typeof maybeFrame.collapse !== 'boolean') {\n        throw new Error('Expected stack frame `collapse` to be a boolean.');\n      }\n      collapse = maybeFrame.collapse;\n    }\n    stack.push({\n      arguments: maybeFrame.arguments?.map((arg) => String(arg)) ?? [],\n      column: maybeFrame.column,\n      file: maybeFrame.file,\n      lineNumber: maybeFrame.lineNumber,\n      methodName: maybeFrame.methodName,\n      collapse,\n    });\n  }\n  return { stack, codeFrame };\n};\n\nexport function deleteStack(stack: StackFrame[]): void {\n  cache.delete(stack);\n}\n\nexport function symbolicate(stack: StackFrame[]): Promise<SymbolicatedStackTrace> {\n  let promise = cache.get(stack);\n  if (promise == null) {\n    promise = symbolicateStackTrace(stack).then(sanitize);\n    cache.set(stack, promise);\n  }\n\n  return promise;\n}\n\nasync function symbolicateStackTrace(stack: UpstreamStackFrame[]): Promise<SymbolicatedStackTrace> {\n  const baseUrl =\n    typeof window === 'undefined'\n      ? process.env.EXPO_DEV_SERVER_ORIGIN\n      : window.location.protocol + '//' + window.location.host;\n\n  return fetch(baseUrl + '/symbolicate', {\n    method: 'POST',\n    body: JSON.stringify({ stack }),\n  }).then((res) => res.json());\n}\n\nexport function parseErrorStack(stack?: string): (UpstreamStackFrame & { collapse?: boolean })[] {\n  if (stack == null) {\n    return [];\n  }\n  if (Array.isArray(stack)) {\n    return stack;\n  }\n\n  return parse(stack).map((frame) => {\n    // Add back support for Hermes native calls:\n    // `    at apply (native)`\n    // Which are parsed to:\n    // {\n    //   \"file\": null,\n    //   \"methodName\": \"apply\",\n    //   \"arguments\": [\"native\"],\n    //   \"lineNumber\": null,\n    //   \"column\": null,\n    //   \"collapse\": false\n    // },\n    // https://github.com/facebook/react-native/blob/f0ad39446404bb6e027d0c486b579c312f35180a/packages/react-native/Libraries/Core/Devtools/parseHermesStack.js#L70\n    if (frame.file == null && frame.arguments?.length === 1 && frame.arguments[0] === 'native') {\n      // Use `<native>` to match the `<anonymous>` and `<unknown>` used by other runtimes.\n      frame.file = '<native>';\n      frame.arguments = [];\n    }\n\n    // frame.file will mostly look like `http://localhost:8081/index.bundle?platform=web&dev=true&hot=false`\n    return {\n      ...frame,\n      column: frame.column != null ? frame.column - 1 : null,\n    };\n  });\n}\n"], "names": ["deleteStack", "parseError<PERSON>tack", "symbolicate", "cache", "Map", "sanitize", "stack", "maybeStack", "codeFrame", "Array", "isArray", "Error", "<PERSON><PERSON><PERSON><PERSON>", "collapse", "push", "arguments", "map", "arg", "String", "column", "file", "lineNumber", "methodName", "delete", "promise", "get", "symbolicateStackTrace", "then", "set", "baseUrl", "window", "process", "env", "EXPO_DEV_SERVER_ORIGIN", "location", "protocol", "host", "fetch", "method", "body", "JSON", "stringify", "res", "json", "parse", "frame", "length"], "mappings": "AAAA;;;;;;CAMC;;;;;;;;;;;IA0DeA,WAAW;eAAXA;;IA0BAC,eAAe;eAAfA;;IAtBAC,WAAW;eAAXA;;;;yBA7DwC;;;;;;AAwBxD,MAAMC,QAA4D,IAAIC;AAEtE;;CAEC,GACD,MAAMC,WAAW,CAAC,EAChBC,OAAOC,UAAU,EACjBC,SAAS,EACc;IACvB,IAAI,CAACC,MAAMC,OAAO,CAACH,aAAa;QAC9B,MAAM,IAAII,MAAM;IAClB;IACA,MAAML,QAAsB,EAAE;IAC9B,KAAK,MAAMM,cAAcL,WAAY;YAStBK;QARb,IAAIC,WAAW;QACf,IAAI,cAAcD,YAAY;YAC5B,IAAI,OAAOA,WAAWC,QAAQ,KAAK,WAAW;gBAC5C,MAAM,IAAIF,MAAM;YAClB;Y<PERSON>A<PERSON>,WAAWD,WAAWC,QAAQ;QAChC;QACAP,MAAMQ,IAAI,CAAC;YACTC,WAAWH,EAAAA,wBAAAA,WAAWG,SAAS,qBAApBH,sBAAsBI,GAAG,CAAC,CAACC,MAAQC,OAAOD,UAAS,EAAE;YAChEE,QAAQP,WAAWO,MAAM;YACzBC,MAAMR,WAAWQ,IAAI;YACrBC,YAAYT,WAAWS,UAAU;YACjCC,YAAYV,WAAWU,UAAU;YACjCT;QACF;IACF;IACA,OAAO;QAAEP;QAAOE;IAAU;AAC5B;AAEO,SAASR,YAAYM,KAAmB;IAC7CH,MAAMoB,MAAM,CAACjB;AACf;AAEO,SAASJ,YAAYI,KAAmB;IAC7C,IAAIkB,UAAUrB,MAAMsB,GAAG,CAACnB;IACxB,IAAIkB,WAAW,MAAM;QACnBA,UAAUE,sBAAsBpB,OAAOqB,IAAI,CAACtB;QAC5CF,MAAMyB,GAAG,CAACtB,OAAOkB;IACnB;IAEA,OAAOA;AACT;AAEA,eAAeE,sBAAsBpB,KAA2B;IAC9D,MAAMuB,UACJ,OAAOC,WAAW,cACdC,QAAQC,GAAG,CAACC,sBAAsB,GAClCH,OAAOI,QAAQ,CAACC,QAAQ,GAAG,OAAOL,OAAOI,QAAQ,CAACE,IAAI;IAE5D,OAAOC,MAAMR,UAAU,gBAAgB;QACrCS,QAAQ;QACRC,MAAMC,KAAKC,SAAS,CAAC;YAAEnC;QAAM;IAC/B,GAAGqB,IAAI,CAAC,CAACe,MAAQA,IAAIC,IAAI;AAC3B;AAEO,SAAS1C,gBAAgBK,KAAc;IAC5C,IAAIA,SAAS,MAAM;QACjB,OAAO,EAAE;IACX;IACA,IAAIG,MAAMC,OAAO,CAACJ,QAAQ;QACxB,OAAOA;IACT;IAEA,OAAOsC,IAAAA,yBAAK,EAACtC,OAAOU,GAAG,CAAC,CAAC6B;YAaGA;QAZ1B,4CAA4C;QAC5C,0BAA0B;QAC1B,uBAAuB;QACvB,IAAI;QACJ,kBAAkB;QAClB,2BAA2B;QAC3B,6BAA6B;QAC7B,wBAAwB;QACxB,oBAAoB;QACpB,sBAAsB;QACtB,KAAK;QACL,+JAA+J;QAC/J,IAAIA,MAAMzB,IAAI,IAAI,QAAQyB,EAAAA,mBAAAA,MAAM9B,SAAS,qBAAf8B,iBAAiBC,MAAM,MAAK,KAAKD,MAAM9B,SAAS,CAAC,EAAE,KAAK,UAAU;YAC1F,oFAAoF;YACpF8B,MAAMzB,IAAI,GAAG;YACbyB,MAAM9B,SAAS,GAAG,EAAE;QACtB;QAEA,wGAAwG;QACxG,OAAO;YACL,GAAG8B,KAAK;YACR1B,QAAQ0B,MAAM1B,MAAM,IAAI,OAAO0B,MAAM1B,MAAM,GAAG,IAAI;QACpD;IACF;AACF"}