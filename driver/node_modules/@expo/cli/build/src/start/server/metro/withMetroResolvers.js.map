{"version": 3, "sources": ["../../../../../src/start/server/metro/withMetroResolvers.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport chalk from 'chalk';\nimport { ConfigT as MetroConfig } from 'metro-config';\nimport type { ResolutionContext, CustomResolutionContext } from 'metro-resolver';\nimport * as metroResolver from 'metro-resolver';\nimport path from 'path';\n\nimport { isFailedToResolveNameError, isFailedToResolvePathError } from './metroErrors';\nimport { env } from '../../../utils/env';\n\nconst debug = require('debug')('expo:metro:withMetroResolvers') as typeof console.log;\n\nexport type MetroResolver = NonNullable<MetroConfig['resolver']['resolveRequest']>;\n\n/** Expo Metro Resolvers can return `null` to skip without throwing an error. Metro Resolvers will throw either a `FailedToResolveNameError` or `FailedToResolvePathError`. */\nexport type ExpoCustomMetroResolver = (\n  ...args: Parameters<MetroResolver>\n) => ReturnType<MetroResolver> | null;\n\n/** @returns `MetroResolver` utilizing the upstream `resolve` method. */\nexport function getDefaultMetroResolver(projectRoot: string): MetroResolver {\n  return (context: ResolutionContext, moduleName: string, platform: string | null) => {\n    return metroResolver.resolve(context, moduleName, platform);\n  };\n}\n\nfunction optionsKeyForContext(context: ResolutionContext) {\n  const canonicalize = require('metro-core/src/canonicalize');\n\n  // Compound key for the resolver cache\n  return JSON.stringify(context.customResolverOptions ?? {}, canonicalize) ?? '';\n}\n\n/**\n * Extend the Metro config `resolver.resolveRequest` method with additional resolvers that can\n * exit early by returning a `Resolution` or skip to the next resolver by returning `null`.\n *\n * @param config Metro config.\n * @param resolvers custom MetroResolver to chain.\n * @returns a new `MetroConfig` with the `resolver.resolveRequest` method chained.\n */\nexport function withMetroResolvers(\n  config: MetroConfig,\n  resolvers: ExpoCustomMetroResolver[]\n): MetroConfig {\n  debug(\n    `Appending ${\n      resolvers.length\n    } custom resolvers to Metro config. (has custom resolver: ${!!config.resolver?.resolveRequest})`\n  );\n  // const hasUserDefinedResolver = !!config.resolver?.resolveRequest;\n  // const defaultResolveRequest = getDefaultMetroResolver(projectRoot);\n  const originalResolveRequest = config.resolver?.resolveRequest;\n\n  return {\n    ...config,\n    resolver: {\n      ...config.resolver,\n      resolveRequest(context, moduleName, platform) {\n        const upstreamResolveRequest = context.resolveRequest;\n\n        const universalContext = {\n          ...context,\n          resolveRequest(\n            ctx: CustomResolutionContext,\n            moduleName: string,\n            platform: string | null\n          ) {\n            for (const resolver of resolvers) {\n              try {\n                const res = resolver(ctx, moduleName, platform);\n                if (res) {\n                  return res;\n                }\n              } catch (error: any) {\n                // If the error is directly related to a resolver not being able to resolve a module, then\n                // we can ignore the error and try the next resolver. Otherwise, we should throw the error.\n                const isResolutionError =\n                  isFailedToResolveNameError(error) || isFailedToResolvePathError(error);\n                if (!isResolutionError) {\n                  throw error;\n                }\n                debug(\n                  `Custom resolver (${resolver.name || '<anonymous>'}) threw: ${error.constructor.name}. (module: ${moduleName}, platform: ${platform}, env: ${ctx.customResolverOptions?.environment}, origin: ${ctx.originModulePath})`\n                );\n              }\n            }\n            // If we haven't returned by now, use the original resolver or upstream resolver.\n            return upstreamResolveRequest(ctx, moduleName, platform);\n          },\n        };\n\n        // If the user defined a resolver, run it first and depend on the documented\n        // chaining logic: https://facebook.github.io/metro/docs/resolution/#resolution-algorithm\n        //\n        // config.resolver.resolveRequest = (context, moduleName, platform) => {\n        //\n        //  // Do work...\n        //\n        //  return context.resolveRequest(context, moduleName, platform);\n        // };\n        const firstResolver = originalResolveRequest ?? universalContext.resolveRequest;\n        return firstResolver(universalContext, moduleName, platform);\n      },\n    },\n  };\n}\n\n/**\n * Hook into the Metro resolver chain and mutate the context so users can resolve against our custom assumptions.\n * For example, this will set `preferNativePlatform` to false when bundling for web.\n * */\nexport function withMetroMutatedResolverContext(\n  config: MetroConfig,\n  getContext: (\n    ctx: CustomResolutionContext,\n    moduleName: string,\n    platform: string | null\n  ) => CustomResolutionContext\n): MetroConfig {\n  const defaultResolveRequest = getDefaultMetroResolver(config.projectRoot);\n  const originalResolveRequest = config.resolver?.resolveRequest;\n\n  return {\n    ...config,\n    resolver: {\n      ...config.resolver,\n      resolveRequest(context, moduleName, platform) {\n        const universalContext = getContext(context, moduleName, platform);\n        const firstResolver =\n          originalResolveRequest ?? universalContext.resolveRequest ?? defaultResolveRequest;\n        return firstResolver(universalContext, moduleName, platform);\n      },\n    },\n  };\n}\n\nexport function withMetroErrorReportingResolver(config: MetroConfig): MetroConfig {\n  if (!env.EXPO_METRO_UNSTABLE_ERRORS) {\n    return config;\n  }\n\n  const originalResolveRequest = config.resolver?.resolveRequest;\n\n  function mutateResolutionError(\n    error: Error,\n    context: ResolutionContext,\n    moduleName: string,\n    platform: string | null\n  ) {\n    const inputPlatform = platform ?? 'null';\n\n    const mapByOrigin = depGraph.get(optionsKeyForContext(context));\n    const mapByPlatform = mapByOrigin?.get(inputPlatform);\n\n    if (!mapByPlatform) {\n      return error;\n    }\n\n    // collect all references inversely using some expensive lookup\n\n    const getReferences = (origin: string) => {\n      const inverseOrigin: { origin: string; previous: string; request: string }[] = [];\n\n      if (!mapByPlatform) {\n        return inverseOrigin;\n      }\n\n      for (const [originKey, mapByTarget] of mapByPlatform) {\n        // search comparing origin to path\n\n        const found = [...mapByTarget.values()].find((resolution) => resolution.path === origin);\n        if (found) {\n          inverseOrigin.push({\n            origin,\n            previous: originKey,\n            request: found.request,\n          });\n        }\n      }\n\n      return inverseOrigin;\n    };\n\n    const pad = (num: number) => {\n      return new Array(num).fill(' ').join('');\n    };\n\n    const root = config.server?.unstable_serverRoot ?? config.projectRoot;\n\n    type InverseDepResult = {\n      origin: string;\n      request: string;\n      previous: InverseDepResult[];\n    };\n    const recurseBackWithLimit = (\n      req: { origin: string; request: string },\n      limit: number,\n      count: number = 0\n    ) => {\n      const results: InverseDepResult = {\n        origin: req.origin,\n        request: req.request,\n        previous: [],\n      };\n\n      if (count >= limit) {\n        return results;\n      }\n\n      const inverse = getReferences(req.origin);\n      for (const match of inverse) {\n        // Use more qualified name if possible\n        // results.origin = match.origin;\n        // Found entry point\n        if (req.origin === match.previous) {\n          continue;\n        }\n        results.previous.push(\n          recurseBackWithLimit({ origin: match.previous, request: match.request }, limit, count + 1)\n        );\n      }\n      return results;\n    };\n\n    const inverseTree = recurseBackWithLimit(\n      { origin: context.originModulePath, request: moduleName },\n      // TODO: Do we need to expose this?\n      35\n    );\n\n    if (inverseTree.previous.length > 0) {\n      debug('Found inverse graph:', JSON.stringify(inverseTree, null, 2));\n      let extraMessage = chalk.bold('Import stack:');\n      const printRecursive = (tree: InverseDepResult, depth: number = 0) => {\n        let filename = path.relative(root, tree.origin);\n        if (filename.match(/\\?ctx=[\\w\\d]+$/)) {\n          filename = filename.replace(/\\?ctx=[\\w\\d]+$/, chalk.dim(' (require.context)'));\n        } else {\n          let formattedRequest = chalk.green(`\"${tree.request}\"`);\n\n          if (\n            // If bundling for web and the import is pulling internals from outside of react-native\n            // then mark it as an invalid import.\n            inputPlatform === 'web' &&\n            !/^(node_modules\\/)?react-native\\//.test(filename) &&\n            tree.request.match(/^react-native\\/.*/)\n          ) {\n            formattedRequest =\n              formattedRequest +\n              chalk`\\n          {yellow Importing react-native internals is not supported on web.}`;\n          }\n\n          filename = filename + chalk`\\n{gray  |} {cyan import} ${formattedRequest}\\n`;\n        }\n        let line = '\\n' + pad(depth) + chalk.gray(' ') + filename;\n        if (filename.match(/node_modules/)) {\n          line = chalk.gray(\n            // Bold the node module name\n            line.replace(/node_modules\\/([^/]+)/, (_match, p1) => {\n              return 'node_modules/' + chalk.bold(p1);\n            })\n          );\n        }\n        extraMessage += line;\n        for (const child of tree.previous) {\n          printRecursive(\n            child,\n            // Only add depth if there are multiple children\n            tree.previous.length > 1 ? depth + 1 : depth\n          );\n        }\n      };\n      printRecursive(inverseTree);\n\n      debug('inverse graph message:', extraMessage);\n\n      // @ts-expect-error\n      error._expoImportStack = extraMessage;\n    } else {\n      debug('Found no inverse tree for:', context.originModulePath);\n    }\n\n    return error;\n  }\n\n  const depGraph: Map<\n    // custom options\n    string,\n    Map<\n      // platform\n      string,\n      Map<\n        // origin module name\n        string,\n        Set<{\n          // required module name\n          path: string;\n          // This isn't entirely accurate since a module can be imported multiple times in a file,\n          // and use different names. But it's good enough for now.\n          request: string;\n        }>\n      >\n    >\n  > = new Map();\n\n  return {\n    ...config,\n    resolver: {\n      ...config.resolver,\n      resolveRequest(context, moduleName, platform) {\n        const storeResult = (res: NonNullable<ReturnType<ExpoCustomMetroResolver>>) => {\n          const inputPlatform = platform ?? 'null';\n\n          const key = optionsKeyForContext(context);\n          if (!depGraph.has(key)) depGraph.set(key, new Map());\n          const mapByTarget = depGraph.get(key);\n          if (!mapByTarget!.has(inputPlatform)) mapByTarget!.set(inputPlatform, new Map());\n          const mapByPlatform = mapByTarget!.get(inputPlatform);\n          if (!mapByPlatform!.has(context.originModulePath))\n            mapByPlatform!.set(context.originModulePath, new Set());\n          const setForModule = mapByPlatform!.get(context.originModulePath)!;\n\n          const qualifiedModuleName = res?.type === 'sourceFile' ? res.filePath : moduleName;\n          setForModule.add({ path: qualifiedModuleName, request: moduleName });\n        };\n\n        // If the user defined a resolver, run it first and depend on the documented\n        // chaining logic: https://facebook.github.io/metro/docs/resolution/#resolution-algorithm\n        //\n        // config.resolver.resolveRequest = (context, moduleName, platform) => {\n        //\n        //  // Do work...\n        //\n        //  return context.resolveRequest(context, moduleName, platform);\n        // };\n        try {\n          const firstResolver = originalResolveRequest ?? context.resolveRequest;\n          const res = firstResolver(context, moduleName, platform);\n          storeResult(res);\n          return res;\n        } catch (error: any) {\n          throw mutateResolutionError(error, context, moduleName, platform);\n        }\n      },\n    },\n  };\n}\n"], "names": ["getDefaultMetroResolver", "withMetroErrorReportingResolver", "withMetroMutatedResolverContext", "withMetroResolvers", "debug", "require", "projectRoot", "context", "moduleName", "platform", "metroResolver", "resolve", "optionsKeyForContext", "canonicalize", "JSON", "stringify", "customResolverOptions", "config", "resolvers", "length", "resolver", "resolveRequest", "originalResolveRequest", "upstreamResolveRequest", "universalContext", "ctx", "res", "error", "isResolutionError", "isFailedToResolveNameError", "isFailedToResolvePathError", "name", "constructor", "environment", "originModulePath", "firstResolver", "getContext", "defaultResolveRequest", "env", "EXPO_METRO_UNSTABLE_ERRORS", "mutateResolutionError", "inputPlatform", "mapByOrigin", "depGraph", "get", "mapByPlatform", "getReferences", "origin", "inverse<PERSON><PERSON>in", "<PERSON><PERSON><PERSON>", "mapByTarget", "found", "values", "find", "resolution", "path", "push", "previous", "request", "pad", "num", "Array", "fill", "join", "root", "server", "unstable_serverRoot", "recurseBackWithLimit", "req", "limit", "count", "results", "inverse", "match", "inverseTree", "extraMessage", "chalk", "bold", "printRecursive", "tree", "depth", "filename", "relative", "replace", "dim", "formattedRequest", "green", "test", "line", "gray", "_match", "p1", "child", "_expoImportStack", "Map", "storeResult", "key", "has", "set", "Set", "setForModule", "qualifiedModuleName", "type", "filePath", "add"], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;IAoBeA,uBAAuB;eAAvBA;;IAqHAC,+BAA+B;eAA/BA;;IAzBAC,+BAA+B;eAA/BA;;IAvEAC,kBAAkB;eAAlBA;;;;gEAxCE;;;;;;;iEAGa;;;;;;;gEACd;;;;;;6BAEsD;qBACnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpB,MAAMC,QAAQC,QAAQ,SAAS;AAUxB,SAASL,wBAAwBM,WAAmB;IACzD,OAAO,CAACC,SAA4BC,YAAoBC;QACtD,OAAOC,iBAAcC,OAAO,CAACJ,SAASC,YAAYC;IACpD;AACF;AAEA,SAASG,qBAAqBL,OAA0B;IACtD,MAAMM,eAAeR,QAAQ;IAE7B,sCAAsC;IACtC,OAAOS,KAAKC,SAAS,CAACR,QAAQS,qBAAqB,IAAI,CAAC,GAAGH,iBAAiB;AAC9E;AAUO,SAASV,mBACdc,MAAmB,EACnBC,SAAoC;QAK4BD,kBAIjCA;IAP/Bb,MACE,CAAC,UAAU,EACTc,UAAUC,MAAM,CACjB,yDAAyD,EAAE,CAAC,GAACF,mBAAAA,OAAOG,QAAQ,qBAAfH,iBAAiBI,cAAc,EAAC,CAAC,CAAC;IAElG,oEAAoE;IACpE,sEAAsE;IACtE,MAAMC,0BAAyBL,oBAAAA,OAAOG,QAAQ,qBAAfH,kBAAiBI,cAAc;IAE9D,OAAO;QACL,GAAGJ,MAAM;QACTG,UAAU;YACR,GAAGH,OAAOG,QAAQ;YAClBC,gBAAed,OAAO,EAAEC,UAAU,EAAEC,QAAQ;gBAC1C,MAAMc,yBAAyBhB,QAAQc,cAAc;gBAErD,MAAMG,mBAAmB;oBACvB,GAAGjB,OAAO;oBACVc,gBACEI,GAA4B,EAC5BjB,UAAkB,EAClBC,QAAuB;wBAEvB,KAAK,MAAMW,YAAYF,UAAW;4BAChC,IAAI;gCACF,MAAMQ,MAAMN,SAASK,KAAKjB,YAAYC;gCACtC,IAAIiB,KAAK;oCACP,OAAOA;gCACT;4BACF,EAAE,OAAOC,OAAY;oCAS4HF;gCAR/I,0FAA0F;gCAC1F,2FAA2F;gCAC3F,MAAMG,oBACJC,IAAAA,uCAA0B,EAACF,UAAUG,IAAAA,uCAA0B,EAACH;gCAClE,IAAI,CAACC,mBAAmB;oCACtB,MAAMD;gCACR;gCACAvB,MACE,CAAC,iBAAiB,EAAEgB,SAASW,IAAI,IAAI,cAAc,SAAS,EAAEJ,MAAMK,WAAW,CAACD,IAAI,CAAC,WAAW,EAAEvB,WAAW,YAAY,EAAEC,SAAS,OAAO,GAAEgB,6BAAAA,IAAIT,qBAAqB,qBAAzBS,2BAA2BQ,WAAW,CAAC,UAAU,EAAER,IAAIS,gBAAgB,CAAC,CAAC,CAAC;4BAE3N;wBACF;wBACA,iFAAiF;wBACjF,OAAOX,uBAAuBE,KAAKjB,YAAYC;oBACjD;gBACF;gBAEA,4EAA4E;gBAC5E,yFAAyF;gBACzF,EAAE;gBACF,wEAAwE;gBACxE,EAAE;gBACF,iBAAiB;gBACjB,EAAE;gBACF,iEAAiE;gBACjE,KAAK;gBACL,MAAM0B,gBAAgBb,0BAA0BE,iBAAiBH,cAAc;gBAC/E,OAAOc,cAAcX,kBAAkBhB,YAAYC;YACrD;QACF;IACF;AACF;AAMO,SAASP,gCACde,MAAmB,EACnBmB,UAI4B;QAGGnB;IAD/B,MAAMoB,wBAAwBrC,wBAAwBiB,OAAOX,WAAW;IACxE,MAAMgB,0BAAyBL,mBAAAA,OAAOG,QAAQ,qBAAfH,iBAAiBI,cAAc;IAE9D,OAAO;QACL,GAAGJ,MAAM;QACTG,UAAU;YACR,GAAGH,OAAOG,QAAQ;YAClBC,gBAAed,OAAO,EAAEC,UAAU,EAAEC,QAAQ;gBAC1C,MAAMe,mBAAmBY,WAAW7B,SAASC,YAAYC;gBACzD,MAAM0B,gBACJb,0BAA0BE,iBAAiBH,cAAc,IAAIgB;gBAC/D,OAAOF,cAAcX,kBAAkBhB,YAAYC;YACrD;QACF;IACF;AACF;AAEO,SAASR,gCAAgCgB,MAAmB;QAKlCA;IAJ/B,IAAI,CAACqB,QAAG,CAACC,0BAA0B,EAAE;QACnC,OAAOtB;IACT;IAEA,MAAMK,0BAAyBL,mBAAAA,OAAOG,QAAQ,qBAAfH,iBAAiBI,cAAc;IAE9D,SAASmB,sBACPb,KAAY,EACZpB,OAA0B,EAC1BC,UAAkB,EAClBC,QAAuB;YAwCVQ;QAtCb,MAAMwB,gBAAgBhC,YAAY;QAElC,MAAMiC,cAAcC,SAASC,GAAG,CAAChC,qBAAqBL;QACtD,MAAMsC,gBAAgBH,+BAAAA,YAAaE,GAAG,CAACH;QAEvC,IAAI,CAACI,eAAe;YAClB,OAAOlB;QACT;QAEA,+DAA+D;QAE/D,MAAMmB,gBAAgB,CAACC;YACrB,MAAMC,gBAAyE,EAAE;YAEjF,IAAI,CAACH,eAAe;gBAClB,OAAOG;YACT;YAEA,KAAK,MAAM,CAACC,WAAWC,YAAY,IAAIL,cAAe;gBACpD,kCAAkC;gBAElC,MAAMM,QAAQ;uBAAID,YAAYE,MAAM;iBAAG,CAACC,IAAI,CAAC,CAACC,aAAeA,WAAWC,IAAI,KAAKR;gBACjF,IAAII,OAAO;oBACTH,cAAcQ,IAAI,CAAC;wBACjBT;wBACAU,UAAUR;wBACVS,SAASP,MAAMO,OAAO;oBACxB;gBACF;YACF;YAEA,OAAOV;QACT;QAEA,MAAMW,MAAM,CAACC;YACX,OAAO,IAAIC,MAAMD,KAAKE,IAAI,CAAC,KAAKC,IAAI,CAAC;QACvC;QAEA,MAAMC,OAAO/C,EAAAA,iBAAAA,OAAOgD,MAAM,qBAAbhD,eAAeiD,mBAAmB,KAAIjD,OAAOX,WAAW;QAOrE,MAAM6D,uBAAuB,CAC3BC,KACAC,OACAC,QAAgB,CAAC;YAEjB,MAAMC,UAA4B;gBAChCxB,QAAQqB,IAAIrB,MAAM;gBAClBW,SAASU,IAAIV,OAAO;gBACpBD,UAAU,EAAE;YACd;YAEA,IAAIa,SAASD,OAAO;gBAClB,OAAOE;YACT;YAEA,MAAMC,UAAU1B,cAAcsB,IAAIrB,MAAM;YACxC,KAAK,MAAM0B,SAASD,QAAS;gBAC3B,sCAAsC;gBACtC,iCAAiC;gBACjC,oBAAoB;gBACpB,IAAIJ,IAAIrB,MAAM,KAAK0B,MAAMhB,QAAQ,EAAE;oBACjC;gBACF;gBACAc,QAAQd,QAAQ,CAACD,IAAI,CACnBW,qBAAqB;oBAAEpB,QAAQ0B,MAAMhB,QAAQ;oBAAEC,SAASe,MAAMf,OAAO;gBAAC,GAAGW,OAAOC,QAAQ;YAE5F;YACA,OAAOC;QACT;QAEA,MAAMG,cAAcP,qBAClB;YAAEpB,QAAQxC,QAAQ2B,gBAAgB;YAAEwB,SAASlD;QAAW,GACxD,mCAAmC;QACnC;QAGF,IAAIkE,YAAYjB,QAAQ,CAACtC,MAAM,GAAG,GAAG;YACnCf,MAAM,wBAAwBU,KAAKC,SAAS,CAAC2D,aAAa,MAAM;YAChE,IAAIC,eAAeC,gBAAK,CAACC,IAAI,CAAC;YAC9B,MAAMC,iBAAiB,CAACC,MAAwBC,QAAgB,CAAC;gBAC/D,IAAIC,WAAW1B,eAAI,CAAC2B,QAAQ,CAAClB,MAAMe,KAAKhC,MAAM;gBAC9C,IAAIkC,SAASR,KAAK,CAAC,mBAAmB;oBACpCQ,WAAWA,SAASE,OAAO,CAAC,kBAAkBP,gBAAK,CAACQ,GAAG,CAAC;gBAC1D,OAAO;oBACL,IAAIC,mBAAmBT,gBAAK,CAACU,KAAK,CAAC,CAAC,CAAC,EAAEP,KAAKrB,OAAO,CAAC,CAAC,CAAC;oBAEtD,IACE,uFAAuF;oBACvF,qCAAqC;oBACrCjB,kBAAkB,SAClB,CAAC,mCAAmC8C,IAAI,CAACN,aACzCF,KAAKrB,OAAO,CAACe,KAAK,CAAC,sBACnB;wBACAY,mBACEA,mBACAT,IAAAA,gBAAK,CAAA,CAAC,8EAA8E,CAAC;oBACzF;oBAEAK,WAAWA,WAAWL,IAAAA,gBAAK,CAAA,CAAC,0BAA0B,EAAES,iBAAiB,EAAE,CAAC;gBAC9E;gBACA,IAAIG,OAAO,OAAO7B,IAAIqB,SAASJ,gBAAK,CAACa,IAAI,CAAC,OAAOR;gBACjD,IAAIA,SAASR,KAAK,CAAC,iBAAiB;oBAClCe,OAAOZ,gBAAK,CAACa,IAAI,CACf,4BAA4B;oBAC5BD,KAAKL,OAAO,CAAC,yBAAyB,CAACO,QAAQC;wBAC7C,OAAO,kBAAkBf,gBAAK,CAACC,IAAI,CAACc;oBACtC;gBAEJ;gBACAhB,gBAAgBa;gBAChB,KAAK,MAAMI,SAASb,KAAKtB,QAAQ,CAAE;oBACjCqB,eACEc,OACA,gDAAgD;oBAChDb,KAAKtB,QAAQ,CAACtC,MAAM,GAAG,IAAI6D,QAAQ,IAAIA;gBAE3C;YACF;YACAF,eAAeJ;YAEftE,MAAM,0BAA0BuE;YAEhC,mBAAmB;YACnBhD,MAAMkE,gBAAgB,GAAGlB;QAC3B,OAAO;YACLvE,MAAM,8BAA8BG,QAAQ2B,gBAAgB;QAC9D;QAEA,OAAOP;IACT;IAEA,MAAMgB,WAkBF,IAAImD;IAER,OAAO;QACL,GAAG7E,MAAM;QACTG,UAAU;YACR,GAAGH,OAAOG,QAAQ;YAClBC,gBAAed,OAAO,EAAEC,UAAU,EAAEC,QAAQ;gBAC1C,MAAMsF,cAAc,CAACrE;oBACnB,MAAMe,gBAAgBhC,YAAY;oBAElC,MAAMuF,MAAMpF,qBAAqBL;oBACjC,IAAI,CAACoC,SAASsD,GAAG,CAACD,MAAMrD,SAASuD,GAAG,CAACF,KAAK,IAAIF;oBAC9C,MAAM5C,cAAcP,SAASC,GAAG,CAACoD;oBACjC,IAAI,CAAC9C,YAAa+C,GAAG,CAACxD,gBAAgBS,YAAagD,GAAG,CAACzD,eAAe,IAAIqD;oBAC1E,MAAMjD,gBAAgBK,YAAaN,GAAG,CAACH;oBACvC,IAAI,CAACI,cAAeoD,GAAG,CAAC1F,QAAQ2B,gBAAgB,GAC9CW,cAAeqD,GAAG,CAAC3F,QAAQ2B,gBAAgB,EAAE,IAAIiE;oBACnD,MAAMC,eAAevD,cAAeD,GAAG,CAACrC,QAAQ2B,gBAAgB;oBAEhE,MAAMmE,sBAAsB3E,CAAAA,uBAAAA,IAAK4E,IAAI,MAAK,eAAe5E,IAAI6E,QAAQ,GAAG/F;oBACxE4F,aAAaI,GAAG,CAAC;wBAAEjD,MAAM8C;wBAAqB3C,SAASlD;oBAAW;gBACpE;gBAEA,4EAA4E;gBAC5E,yFAAyF;gBACzF,EAAE;gBACF,wEAAwE;gBACxE,EAAE;gBACF,iBAAiB;gBACjB,EAAE;gBACF,iEAAiE;gBACjE,KAAK;gBACL,IAAI;oBACF,MAAM2B,gBAAgBb,0BAA0Bf,QAAQc,cAAc;oBACtE,MAAMK,MAAMS,cAAc5B,SAASC,YAAYC;oBAC/CsF,YAAYrE;oBACZ,OAAOA;gBACT,EAAE,OAAOC,OAAY;oBACnB,MAAMa,sBAAsBb,OAAOpB,SAASC,YAAYC;gBAC1D;YACF;QACF;IACF;AACF"}