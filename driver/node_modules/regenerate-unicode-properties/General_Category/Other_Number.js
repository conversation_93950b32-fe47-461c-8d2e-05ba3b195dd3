const set = require('regenerate')(0xB9, 0x19DA, 0x2070, 0x2189, 0x2CFD);
set.addRange(0xB2, 0xB3).addRange(0xBC, 0xBE).addRange(0x9F4, 0x9F9).addRange(0xB72, 0xB77).addRange(0xBF0, 0xBF2).addRange(0xC78, 0xC7E).addRange(0xD58, 0xD5E).addRange(0xD70, 0xD78).addRange(0xF2A, 0xF33).addRange(0x1369, 0x137C).addRange(0x17F0, 0x17F9).addRange(0x2074, 0x2079).addRange(0x2080, 0x2089).addRange(0x2150, 0x215F).addRange(0x2460, 0x249B).addRange(0x24EA, 0x24FF).addRange(0x2776, 0x2793).addRange(0x3192, 0x3195).addRange(0x3220, 0x3229).addRange(0x3248, 0x324F).addRange(0x3251, 0x325F).addRange(0x3280, 0x3289).addRange(0x32B1, 0x32BF).addRange(0xA830, 0xA835).addRange(0x10107, 0x10133).addRange(0x10175, 0x10178).addRange(0x1018A, 0x1018B).addRange(0x102E1, 0x102FB).addRange(0x10320, 0x10323).addRange(0x10858, 0x1085F).addRange(0x10879, 0x1087F).addRange(0x108A7, 0x108AF).addRange(0x108FB, 0x108FF).addRange(0x10916, 0x1091B).addRange(0x109BC, 0x109BD).addRange(0x109C0, 0x109CF).addRange(0x109D2, 0x109FF).addRange(0x10A40, 0x10A48).addRange(0x10A7D, 0x10A7E).addRange(0x10A9D, 0x10A9F).addRange(0x10AEB, 0x10AEF).addRange(0x10B58, 0x10B5F).addRange(0x10B78, 0x10B7F).addRange(0x10BA9, 0x10BAF).addRange(0x10CFA, 0x10CFF).addRange(0x10E60, 0x10E7E).addRange(0x10F1D, 0x10F26).addRange(0x10F51, 0x10F54).addRange(0x10FC5, 0x10FCB).addRange(0x11052, 0x11065).addRange(0x111E1, 0x111F4);
set.addRange(0x1173A, 0x1173B).addRange(0x118EA, 0x118F2).addRange(0x11C5A, 0x11C6C).addRange(0x11FC0, 0x11FD4).addRange(0x16B5B, 0x16B61).addRange(0x16E80, 0x16E96).addRange(0x1D2C0, 0x1D2D3).addRange(0x1D2E0, 0x1D2F3).addRange(0x1D360, 0x1D378).addRange(0x1E8C7, 0x1E8CF).addRange(0x1EC71, 0x1ECAB).addRange(0x1ECAD, 0x1ECAF).addRange(0x1ECB1, 0x1ECB4).addRange(0x1ED01, 0x1ED2D).addRange(0x1ED2F, 0x1ED3D).addRange(0x1F100, 0x1F10C);
exports.characters = set;
