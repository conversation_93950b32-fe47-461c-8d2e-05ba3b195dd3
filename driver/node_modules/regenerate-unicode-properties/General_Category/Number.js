const set = require('regenerate')(0xB9, 0x2070, 0x2CFD, 0x3007, 0x10341, 0x1034A);
set.addRange(0x30, 0x39).addRange(0xB2, 0xB3).addRange(0xBC, 0xBE).addRange(0x660, 0x669).addRange(0x6F0, 0x6F9).addRange(0x7C0, 0x7C9).addRange(0x966, 0x96F).addRange(0x9E6, 0x9EF).addRange(0x9F4, 0x9F9).addRange(0xA66, 0xA6F).addRange(0xAE6, 0xAEF).addRange(0xB66, 0xB6F).addRange(0xB72, 0xB77).addRange(0xBE6, 0xBF2).addRange(0xC66, 0xC6F).addRange(0xC78, 0xC7E).addRange(0xCE6, 0xCEF).addRange(0xD58, 0xD5E).addRange(0xD66, 0xD78).addRange(0xDE6, 0xDEF).addRange(0xE50, 0xE59).addRange(0xED0, 0xED9).addRange(0xF20, 0xF33).addRange(0x1040, 0x1049).addRange(0x1090, 0x1099).addRange(0x1369, 0x137C).addRange(0x16EE, 0x16F0).addRange(0x17E0, 0x17E9).addRange(0x17F0, 0x17F9).addRange(0x1810, 0x1819).addRange(0x1946, 0x194F).addRange(0x19D0, 0x19DA).addRange(0x1A80, 0x1A89).addRange(0x1A90, 0x1A99).addRange(0x1B50, 0x1B59).addRange(0x1BB0, 0x1BB9).addRange(0x1C40, 0x1C49).addRange(0x1C50, 0x1C59).addRange(0x2074, 0x2079).addRange(0x2080, 0x2089).addRange(0x2150, 0x2182).addRange(0x2185, 0x2189).addRange(0x2460, 0x249B).addRange(0x24EA, 0x24FF).addRange(0x2776, 0x2793).addRange(0x3021, 0x3029).addRange(0x3038, 0x303A).addRange(0x3192, 0x3195).addRange(0x3220, 0x3229).addRange(0x3248, 0x324F).addRange(0x3251, 0x325F);
set.addRange(0x3280, 0x3289).addRange(0x32B1, 0x32BF).addRange(0xA620, 0xA629).addRange(0xA6E6, 0xA6EF).addRange(0xA830, 0xA835).addRange(0xA8D0, 0xA8D9).addRange(0xA900, 0xA909).addRange(0xA9D0, 0xA9D9).addRange(0xA9F0, 0xA9F9).addRange(0xAA50, 0xAA59).addRange(0xABF0, 0xABF9).addRange(0xFF10, 0xFF19).addRange(0x10107, 0x10133).addRange(0x10140, 0x10178).addRange(0x1018A, 0x1018B).addRange(0x102E1, 0x102FB).addRange(0x10320, 0x10323).addRange(0x103D1, 0x103D5).addRange(0x104A0, 0x104A9).addRange(0x10858, 0x1085F).addRange(0x10879, 0x1087F).addRange(0x108A7, 0x108AF).addRange(0x108FB, 0x108FF).addRange(0x10916, 0x1091B).addRange(0x109BC, 0x109BD).addRange(0x109C0, 0x109CF).addRange(0x109D2, 0x109FF).addRange(0x10A40, 0x10A48).addRange(0x10A7D, 0x10A7E).addRange(0x10A9D, 0x10A9F).addRange(0x10AEB, 0x10AEF).addRange(0x10B58, 0x10B5F).addRange(0x10B78, 0x10B7F).addRange(0x10BA9, 0x10BAF).addRange(0x10CFA, 0x10CFF).addRange(0x10D30, 0x10D39).addRange(0x10D40, 0x10D49).addRange(0x10E60, 0x10E7E).addRange(0x10F1D, 0x10F26).addRange(0x10F51, 0x10F54).addRange(0x10FC5, 0x10FCB).addRange(0x11052, 0x1106F).addRange(0x110F0, 0x110F9).addRange(0x11136, 0x1113F).addRange(0x111D0, 0x111D9).addRange(0x111E1, 0x111F4).addRange(0x112F0, 0x112F9).addRange(0x11450, 0x11459).addRange(0x114D0, 0x114D9).addRange(0x11650, 0x11659).addRange(0x116C0, 0x116C9);
set.addRange(0x116D0, 0x116E3).addRange(0x11730, 0x1173B).addRange(0x118E0, 0x118F2).addRange(0x11950, 0x11959).addRange(0x11BF0, 0x11BF9).addRange(0x11C50, 0x11C6C).addRange(0x11D50, 0x11D59).addRange(0x11DA0, 0x11DA9).addRange(0x11F50, 0x11F59).addRange(0x11FC0, 0x11FD4).addRange(0x12400, 0x1246E).addRange(0x16130, 0x16139).addRange(0x16A60, 0x16A69).addRange(0x16AC0, 0x16AC9).addRange(0x16B50, 0x16B59).addRange(0x16B5B, 0x16B61).addRange(0x16D70, 0x16D79).addRange(0x16E80, 0x16E96).addRange(0x1CCF0, 0x1CCF9).addRange(0x1D2C0, 0x1D2D3).addRange(0x1D2E0, 0x1D2F3).addRange(0x1D360, 0x1D378).addRange(0x1D7CE, 0x1D7FF).addRange(0x1E140, 0x1E149).addRange(0x1E2F0, 0x1E2F9).addRange(0x1E4F0, 0x1E4F9).addRange(0x1E5F1, 0x1E5FA).addRange(0x1E8C7, 0x1E8CF).addRange(0x1E950, 0x1E959).addRange(0x1EC71, 0x1ECAB).addRange(0x1ECAD, 0x1ECAF).addRange(0x1ECB1, 0x1ECB4).addRange(0x1ED01, 0x1ED2D).addRange(0x1ED2F, 0x1ED3D).addRange(0x1F100, 0x1F10C).addRange(0x1FBF0, 0x1FBF9);
exports.characters = set;
