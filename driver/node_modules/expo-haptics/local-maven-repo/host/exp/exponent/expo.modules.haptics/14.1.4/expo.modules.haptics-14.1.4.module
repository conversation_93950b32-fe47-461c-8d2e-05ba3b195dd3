{"formatVersion": "1.1", "component": {"group": "host.exp.exponent", "module": "expo.modules.haptics", "version": "14.1.4", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.13"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.0.0"}}], "files": [{"name": "expo.modules.haptics-14.1.4.aar", "url": "expo.modules.haptics-14.1.4.aar", "size": 28082, "sha512": "d1076b99c0a4998b772ec55092756303b068ccdae385ccaa4baea09ba349bb76e55ff3c83f280cbcc8b98088ca6bf83d8c70416bf31b7b996bf814ee812d8be5", "sha256": "88ed5eb550d7dace4fc16f4c10d51ac4e94b00449d652e772ea8d9e28cb00683", "sha1": "dd05913127a18e1b86d629a59e47e8395b272e86", "md5": "cb62b8b29342f19498cf859ac9e310bd"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7", "version": {"requires": "2.0.21"}}, {"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.0.0"}}], "files": [{"name": "expo.modules.haptics-14.1.4.aar", "url": "expo.modules.haptics-14.1.4.aar", "size": 28082, "sha512": "d1076b99c0a4998b772ec55092756303b068ccdae385ccaa4baea09ba349bb76e55ff3c83f280cbcc8b98088ca6bf83d8c70416bf31b7b996bf814ee812d8be5", "sha256": "88ed5eb550d7dace4fc16f4c10d51ac4e94b00449d652e772ea8d9e28cb00683", "sha1": "dd05913127a18e1b86d629a59e47e8395b272e86", "md5": "cb62b8b29342f19498cf859ac9e310bd"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "expo.modules.haptics-14.1.4-sources.jar", "url": "expo.modules.haptics-14.1.4-sources.jar", "size": 5163, "sha512": "f633d4531c9b82d15c63049e288aa9c626060387bd337d684eeeab8636cf9c66c821715ec4d52f8a25d054296b843e6947d7d644af223e2d1cc89737fc8fea61", "sha256": "9722e38c417c5e51ed9a82b2e3f97aed7f554dd0531707ff0c008771b0637f04", "sha1": "33aab2666ff455cc101de95494ce99e45fc33391", "md5": "f67422521236b067bd723aa3f78be1e6"}]}]}