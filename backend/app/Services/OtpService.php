<?php

namespace App\Services;

use App\Models\Otp;
use Carbon\Carbon;

class OtpService
{
    /**
     * Generate and send OTP for mobile number
     */
    public function generateAndSendOtp(string $mobile, string $type = 'signin'): array
    {
        try {
            // Check rate limiting (max 3 OTPs per mobile per hour)
            $recentOtps = Otp::where('mobile', $mobile)
                ->where('created_at', '>', Carbon::now()->subHour())
                ->count();

            if ($recentOtps >= 3) {
                return [
                    'success' => false,
                    'message' => 'Too many OTP requests. Please try again later.',
                ];
            }

            // Generate OTP
            $otp = Otp::generateOtp($mobile, $type);

            // In a real application, integrate with SMS service here
            // For development, we'll log the OTP
            \Log::info("OTP for {$mobile}: {$otp}");

            return [
                'success' => true,
                'message' => 'OTP sent successfully.',
                'otp' => config('app.debug') ? $otp : null, // Only return OTP in debug mode
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to send OTP: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Verify OTP for mobile number
     */
    public function verifyOtp(string $mobile, string $otp): array
    {
        try {
            $isValid = Otp::verifyOtp($mobile, $otp);

            if ($isValid) {
                return [
                    'success' => true,
                    'message' => 'OTP verified successfully.',
                ];
            }

            return [
                'success' => false,
                'message' => 'Invalid or expired OTP.',
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'OTP verification failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check if mobile number has pending OTP
     */
    public function hasPendingOtp(string $mobile): bool
    {
        return Otp::where('mobile', $mobile)
            ->where('is_used', false)
            ->where('expires_at', '>', Carbon::now())
            ->exists();
    }

    /**
     * Get remaining time for OTP expiry
     */
    public function getOtpExpiryTime(string $mobile): ?int
    {
        $otp = Otp::where('mobile', $mobile)
            ->where('is_used', false)
            ->where('expires_at', '>', Carbon::now())
            ->orderBy('created_at', 'desc')
            ->first();

        if ($otp) {
            return $otp->expires_at->diffInSeconds(Carbon::now());
        }

        return null;
    }
}
