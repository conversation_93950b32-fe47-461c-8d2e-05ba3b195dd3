<?php

namespace App\Services;

use App\Models\Trip;
use App\Models\Driver;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DriverMatchingService
{
    protected $fcmService;

    public function __construct(FCMNotificationService $fcmService)
    {
        $this->fcmService = $fcmService;
    }

    /**
     * Find and notify nearby drivers for a trip
     */
    public function findAndNotifyDrivers(Trip $trip): void
    {
        $nearbyDrivers = $this->findNearbyDrivers(
            $trip->pickup_latitude,
            $trip->pickup_longitude,
            5.0 // 5km radius
        );

        if ($nearbyDrivers->isEmpty()) {
            Log::warning('No nearby drivers found for trip', ['trip_id' => $trip->id]);
            return;
        }

        // Notify the closest driver first
        $closestDriver = $nearbyDrivers->first();
        $this->fcmService->sendTripRequestNotification($trip, $closestDriver);

        Log::info('Trip request sent to driver', [
            'trip_id' => $trip->id,
            'driver_id' => $closestDriver->id,
            'distance' => $closestDriver->distance
        ]);
    }

    /**
     * Find next available driver after rejection
     */
    public function findNextDriver(Trip $trip, int $rejectedDriverId): void
    {
        $nearbyDrivers = $this->findNearbyDrivers(
            $trip->pickup_latitude,
            $trip->pickup_longitude,
            5.0
        );

        // Filter out the driver who just rejected
        $availableDrivers = $nearbyDrivers->filter(function ($driver) use ($rejectedDriverId) {
            return $driver->id !== $rejectedDriverId;
        });

        if ($availableDrivers->isEmpty()) {
            Log::warning('No more drivers available for trip', ['trip_id' => $trip->id]);
            return;
        }

        // Notify the next closest driver
        $nextDriver = $availableDrivers->first();
        $this->fcmService->sendTripRequestNotification($trip, $nextDriver);

        Log::info('Trip request sent to next driver', [
            'trip_id' => $trip->id,
            'driver_id' => $nextDriver->id,
            'rejected_driver_id' => $rejectedDriverId
        ]);
    }

    /**
     * Find nearby available drivers using Haversine formula
     */
    public function findNearbyDrivers(float $latitude, float $longitude, float $radiusKm = 5.0)
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        return Driver::select('*')
            ->selectRaw("
                (
                    {$earthRadius} * acos(
                        cos(radians(?)) *
                        cos(radians(current_latitude)) *
                        cos(radians(current_longitude) - radians(?)) +
                        sin(radians(?)) *
                        sin(radians(current_latitude))
                    )
                ) AS distance
            ", [$latitude, $longitude, $latitude])
            ->where('is_available', true)
            ->where('is_online', true)
            ->whereNotNull('current_latitude')
            ->whereNotNull('current_longitude')
            ->whereNotNull('fcm_token')
            ->having('distance', '<=', $radiusKm)
            ->orderBy('distance')
            ->get();
    }
}
