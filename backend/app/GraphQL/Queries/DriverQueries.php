<?php

namespace App\GraphQL\Queries;

use App\Models\Driver;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class DriverQueries
{
    /**
     * Get the authenticated driver
     */
    public function me($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo)
    {
        // Get the token from the request
        $request = $context->request();
        $token = $request->bearerToken();

        if (!$token) {
            return null;
        }

        // Find the driver by token
        $tokenModel = \Laravel\Sanctum\PersonalAccessToken::findToken($token);

        if (!$tokenModel || !$tokenModel->tokenable instanceof Driver) {
            return null;
        }

        return $tokenModel->tokenable;
    }
}
