<?php

namespace App\GraphQL\Mutations;

use App\Models\User;
use App\Models\Otp;
use Illuminate\Support\Facades\Hash;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class AuthMutations
{
    /**
     * Handle user signup
     */
    public function userSignup($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): array
    {
        try {
            // Create new user
            $user = User::create([
                'first_name' => $args['first_name'],
                'last_name' => $args['last_name'],
                'mobile' => $args['mobile'],
            ]);

            // Generate OTP
            $otp = Otp::generateOtp($args['mobile'], 'signup', 'user');

            // In a real application, you would send the OTP via SMS here
            // For now, we'll just log it or return success
            \Log::info("OTP for {$args['mobile']}: {$otp}");

            return [
                'success' => true,
                'message' => 'Account created successfully. OTP sent to your mobile number.',
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to create account: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Handle user signin
     */
    public function userSignin($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): array
    {
        try {
            // Check if user exists
            $user = User::findByMobile($args['mobile']);

            if (!$user) {
                return [
                    'success' => false,
                    'message' => 'Mobile number not found. Please sign up first.',
                ];
            }

            // Generate OTP
            $otp = Otp::generateOtp($args['mobile'], 'signin', 'user');

            // In a real application, you would send the OTP via SMS here
            \Log::info("OTP for {$args['mobile']}: {$otp}");

            return [
                'success' => true,
                'message' => 'OTP sent to your mobile number.',
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to send OTP: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Handle OTP verification
     */
    public function userVerifyOtp($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): array
    {
        try {
            // Verify OTP
            $isValidOtp = Otp::verifyOtp($args['mobile'], $args['otp'], 'user');

            if (!$isValidOtp) {
                return [
                    'success' => false,
                    'message' => 'Invalid or expired OTP.',
                    'token' => null,
                    'user' => null,
                ];
            }

            // Find user
            $user = User::findByMobile($args['mobile']);

            if (!$user) {
                return [
                    'success' => false,
                    'message' => 'User not found.',
                    'token' => null,
                    'user' => null,
                ];
            }

            // Mark mobile as verified
            if (!$user->mobile_verified_at) {
                $user->update(['mobile_verified_at' => now()]);
            }

            // Create authentication token
            $token = $user->createToken('mobile-auth')->plainTextToken;

            return [
                'success' => true,
                'message' => 'Authentication successful.',
                'token' => $token,
                'user' => $user,
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Verification failed: ' . $e->getMessage(),
                'token' => null,
                'user' => null,
            ];
        }
    }
}
