<?php

namespace App\GraphQL\Mutations;

use App\Models\Trip;
use App\Models\User;
use App\Models\Driver;
use App\Services\FCMNotificationService;
use App\Services\DriverMatchingService;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class TripMutations
{
    protected $fcmService;
    protected $driverMatchingService;

    public function __construct(FCMNotificationService $fcmService, DriverMatchingService $driverMatchingService)
    {
        $this->fcmService = $fcmService;
        $this->driverMatchingService = $driverMatchingService;
    }

    /**
     * Request a new trip
     */
    public function requestTrip($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): array
    {
        try {
            $user = $context->user();
            if (!$user) {
                return [
                    'success' => false,
                    'message' => 'User not authenticated.',
                    'trip' => null,
                ];
            }

            // Check if user has an active trip
            $activeTrip = Trip::where('user_id', $user->id)
                ->whereIn('status', ['requested', 'driver_assigned', 'driver_arriving', 'driver_arrived', 'trip_started'])
                ->first();

            if ($activeTrip) {
                return [
                    'success' => false,
                    'message' => 'You already have an active trip.',
                    'trip' => null,
                ];
            }

            // Calculate estimated fare and distance
            $distance = Trip::calculateDistance(
                $args['pickupLat'],
                $args['pickupLng'],
                $args['destinationLat'],
                $args['destinationLng']
            );

            $estimatedFare = $this->calculateFare($distance);
            $estimatedDuration = $this->calculateDuration($distance);

            // Create the trip
            $trip = Trip::create([
                'user_id' => $user->id,
                'pickup_latitude' => $args['pickupLat'],
                'pickup_longitude' => $args['pickupLng'],
                'pickup_address' => $args['pickupAddress'],
                'destination_latitude' => $args['destinationLat'],
                'destination_longitude' => $args['destinationLng'],
                'destination_address' => $args['destinationAddress'],
                'status' => 'requested',
                'requested_at' => now(),
                'estimated_fare' => $estimatedFare,
                'estimated_duration' => $estimatedDuration,
                'estimated_distance' => $distance,
            ]);

            // Find and notify nearby drivers
            $this->driverMatchingService->findAndNotifyDrivers($trip);

            return [
                'success' => true,
                'message' => 'Trip requested successfully. Searching for nearby drivers...',
                'trip' => $trip->load('user'),
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to request trip: ' . $e->getMessage(),
                'trip' => null,
            ];
        }
    }

    /**
     * Accept a trip request
     */
    public function acceptTrip($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): array
    {
        try {
            // Get the driver from the token
            $request = $context->request();
            $token = $request->bearerToken();

            if (!$token) {
                return [
                    'success' => false,
                    'message' => 'Driver not authenticated.',
                    'trip' => null,
                ];
            }

            $tokenModel = \Laravel\Sanctum\PersonalAccessToken::findToken($token);
            if (!$tokenModel || !$tokenModel->tokenable instanceof Driver) {
                return [
                    'success' => false,
                    'message' => 'Invalid driver token.',
                    'trip' => null,
                ];
            }

            $driver = $tokenModel->tokenable;

            // Check if driver is available
            if (!$driver->is_available) {
                return [
                    'success' => false,
                    'message' => 'Driver is not available.',
                    'trip' => null,
                ];
            }

            // Find the trip
            $trip = Trip::find($args['tripId']);
            if (!$trip) {
                return [
                    'success' => false,
                    'message' => 'Trip not found.',
                    'trip' => null,
                ];
            }

            // Check if trip is still available
            if ($trip->status !== 'requested') {
                return [
                    'success' => false,
                    'message' => 'Trip is no longer available.',
                    'trip' => null,
                ];
            }

            // Assign driver to trip
            $trip->update([
                'driver_id' => $driver->id,
                'status' => 'driver_assigned',
                'accepted_at' => now(),
            ]);

            // Set driver as unavailable
            $driver->update(['is_available' => false]);

            // Send notification to user
            $this->fcmService->sendDriverAssignedNotification($trip->load(['user', 'driver']));

            return [
                'success' => true,
                'message' => 'Trip accepted successfully.',
                'trip' => $trip->load(['user', 'driver']),
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to accept trip: ' . $e->getMessage(),
                'trip' => null,
            ];
        }
    }

    /**
     * Reject a trip request
     */
    public function rejectTrip($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): array
    {
        try {
            // Get the driver from the token
            $request = $context->request();
            $token = $request->bearerToken();

            if (!$token) {
                return [
                    'success' => false,
                    'message' => 'Driver not authenticated.',
                    'trip' => null,
                ];
            }

            $tokenModel = \Laravel\Sanctum\PersonalAccessToken::findToken($token);
            if (!$tokenModel || !$tokenModel->tokenable instanceof Driver) {
                return [
                    'success' => false,
                    'message' => 'Invalid driver token.',
                    'trip' => null,
                ];
            }

            $driver = $tokenModel->tokenable;

            // Find the trip
            $trip = Trip::find($args['tripId']);
            if (!$trip) {
                return [
                    'success' => false,
                    'message' => 'Trip not found.',
                    'trip' => null,
                ];
            }

            // Increment rejection count
            $trip->increment('rejection_count');

            // Find next driver if rejection count is less than 5
            if ($trip->rejection_count < 5) {
                $this->driverMatchingService->findNextDriver($trip, $driver->id);
            } else {
                // Cancel trip if too many rejections
                $trip->update(['status' => 'cancelled_by_driver']);
            }

            return [
                'success' => true,
                'message' => 'Trip rejected.',
                'trip' => $trip->load(['user', 'driver']),
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to reject trip: ' . $e->getMessage(),
                'trip' => null,
            ];
        }
    }

    /**
     * Update trip status
     */
    public function updateTripStatus($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): array
    {
        try {
            // Get the driver from the token
            $request = $context->request();
            $token = $request->bearerToken();

            if (!$token) {
                return [
                    'success' => false,
                    'message' => 'Driver not authenticated.',
                    'trip' => null,
                ];
            }

            $tokenModel = \Laravel\Sanctum\PersonalAccessToken::findToken($token);
            if (!$tokenModel || !$tokenModel->tokenable instanceof Driver) {
                return [
                    'success' => false,
                    'message' => 'Invalid driver token.',
                    'trip' => null,
                ];
            }

            $driver = $tokenModel->tokenable;

            // Find the trip
            $trip = Trip::find($args['tripId']);
            if (!$trip || $trip->driver_id !== $driver->id) {
                return [
                    'success' => false,
                    'message' => 'Trip not found or not assigned to you.',
                    'trip' => null,
                ];
            }

            $status = strtolower($args['status']);
            $updateData = ['status' => $status];

            // Set appropriate timestamp based on status
            switch ($status) {
                case 'driver_arriving':
                    $this->fcmService->sendDriverArrivingNotification($trip->load(['user', 'driver']));
                    break;
                case 'driver_arrived':
                    $updateData['picked_up_at'] = now();
                    $this->fcmService->sendDriverArrivedNotification($trip->load(['user', 'driver']));
                    break;
                case 'trip_started':
                    $updateData['started_at'] = now();
                    break;
                case 'trip_completed':
                    $updateData['completed_at'] = now();
                    $updateData['actual_fare'] = $trip->estimated_fare; // For now, use estimated fare
                    // Make driver available again
                    $driver->update(['is_available' => true]);
                    break;
            }

            $trip->update($updateData);

            return [
                'success' => true,
                'message' => 'Trip status updated successfully.',
                'trip' => $trip->load(['user', 'driver']),
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to update trip status: ' . $e->getMessage(),
                'trip' => null,
            ];
        }
    }

    /**
     * Cancel a trip
     */
    public function cancelTrip($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): array
    {
        try {
            $user = $context->user();
            if (!$user) {
                return [
                    'success' => false,
                    'message' => 'User not authenticated.',
                    'reason' => null,
                ];
            }

            // Find the trip
            $trip = Trip::find($args['tripId']);
            if (!$trip || $trip->user_id !== $user->id) {
                return [
                    'success' => false,
                    'message' => 'Trip not found or not yours.',
                    'reason' => null,
                ];
            }

            // Check if trip can be cancelled
            if (!$trip->canBeCancelled()) {
                return [
                    'success' => false,
                    'message' => 'Trip cannot be cancelled at this stage.',
                    'reason' => null,
                ];
            }

            // Cancel the trip
            $trip->update(['status' => 'cancelled_by_user']);

            // Make driver available again if assigned
            if ($trip->driver) {
                $trip->driver->update(['is_available' => true]);
            }

            $reason = $args['reason'] ?? 'Cancelled by user';

            return [
                'success' => true,
                'message' => 'Trip cancelled successfully.',
                'reason' => $reason,
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to cancel trip: ' . $e->getMessage(),
                'reason' => null,
            ];
        }
    }

    /**
     * Calculate fare based on distance
     */
    private function calculateFare(float $distance): float
    {
        $baseFare = 5.00; // Base fare
        $perKmRate = 2.50; // Rate per kilometer
        return round($baseFare + ($distance * $perKmRate), 2);
    }

    /**
     * Calculate estimated duration based on distance
     */
    private function calculateDuration(float $distance): int
    {
        $averageSpeed = 30; // km/h average speed in city
        return max(5, round(($distance / $averageSpeed) * 60)); // minimum 5 minutes
    }
}
