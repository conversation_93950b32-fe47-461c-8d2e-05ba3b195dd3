<?php

namespace App\GraphQL\Mutations;

use App\Models\Driver;
use App\Models\Otp;
use GraphQL\Type\Definition\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;

class DriverAuthMutations
{
    /**
     * Handle driver signup
     */
    public function driverSignup($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): array
    {
        try {
            // Create new driver
            $driver = Driver::create([
                'first_name' => $args['first_name'],
                'last_name' => $args['last_name'],
                'mobile' => $args['mobile'],
            ]);

            // Generate OTP
            $otp = Otp::generateOtp($args['mobile'], 'signup', 'driver');

            // In a real application, you would send the OTP via SMS here
            // For now, we'll just log it or return success
            \Log::info("Driver OTP for {$args['mobile']}: {$otp}");

            return [
                'success' => true,
                'message' => 'Driver account created successfully. OTP sent to your mobile number.',
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to create driver account: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Handle driver signin
     */
    public function driverSignin($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): array
    {
        try {
            // Check if driver exists
            $driver = Driver::findByMobile($args['mobile']);

            if (!$driver) {
                return [
                    'success' => false,
                    'message' => 'Mobile number not found. Please sign up first.',
                ];
            }

            // Generate OTP
            $otp = Otp::generateOtp($args['mobile'], 'signin', 'driver');

            // In a real application, you would send the OTP via SMS here
            \Log::info("Driver OTP for {$args['mobile']}: {$otp}");

            return [
                'success' => true,
                'message' => 'OTP sent to your mobile number.',
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to send OTP: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Handle driver OTP verification
     */
    public function driverVerifyOtp($rootValue, array $args, GraphQLContext $context, ResolveInfo $resolveInfo): array
    {
        try {
            // Verify OTP
            $isValidOtp = Otp::verifyOtp($args['mobile'], $args['otp'], 'driver');

            if (!$isValidOtp) {
                return [
                    'success' => false,
                    'message' => 'Invalid or expired OTP.',
                    'token' => null,
                    'user' => null,
                ];
            }

            // Find driver
            $driver = Driver::findByMobile($args['mobile']);

            if (!$driver) {
                return [
                    'success' => false,
                    'message' => 'Driver not found.',
                    'token' => null,
                    'user' => null,
                ];
            }

            // Mark mobile as verified
            if (!$driver->mobile_verified_at) {
                $driver->update(['mobile_verified_at' => now()]);
            }

            // Create authentication token
            $token = $driver->createToken('driver-mobile-auth')->plainTextToken;

            return [
                'success' => true,
                'message' => 'Authentication successful.',
                'token' => $token,
                'user' => $driver,
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Verification failed: ' . $e->getMessage(),
                'token' => null,
                'user' => null,
            ];
        }
    }
}
