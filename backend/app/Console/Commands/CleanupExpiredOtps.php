<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Otp;

class CleanupExpiredOtps extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'otp:cleanup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired OTP records from the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Cleaning up expired OTPs...');

        $deletedCount = Otp::where('expires_at', '<', now())->count();
        Otp::cleanupExpired();

        $this->info("Cleaned up {$deletedCount} expired OTP records.");

        return Command::SUCCESS;
    }
}
