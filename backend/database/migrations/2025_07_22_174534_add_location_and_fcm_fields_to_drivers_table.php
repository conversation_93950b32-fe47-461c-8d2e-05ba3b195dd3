<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('drivers', function (Blueprint $table) {
            $table->decimal('current_latitude', 10, 8)->nullable();
            $table->decimal('current_longitude', 11, 8)->nullable();
            $table->boolean('is_online')->default(false);
            $table->boolean('is_available')->default(false);
            $table->string('fcm_token')->nullable();
            $table->timestamp('last_location_update')->nullable();
            $table->string('vehicle_type')->default('car');
            $table->string('license_plate')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('drivers', function (Blueprint $table) {
            $table->dropColumn([
                'current_latitude',
                'current_longitude',
                'is_online',
                'is_available',
                'fcm_token',
                'last_location_update',
                'vehicle_type',
                'license_plate'
            ]);
        });
    }
};
