<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add mobile authentication fields
            $table->string('first_name')->after('id');
            $table->string('last_name')->after('first_name');
            $table->string('mobile')->unique()->after('last_name');
            $table->timestamp('mobile_verified_at')->nullable()->after('mobile');

            // Make email nullable since we're using mobile for authentication
            $table->string('email')->nullable()->change();
            $table->dropUnique(['email']);

            // Make password nullable since we're using OTP authentication
            $table->string('password')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Remove mobile authentication fields
            $table->dropColumn(['first_name', 'last_name', 'mobile', 'mobile_verified_at']);

            // Restore email as required and unique
            $table->string('email')->nullable(false)->change();
            $table->unique('email');

            // Restore password as required
            $table->string('password')->nullable(false)->change();
        });
    }
};
